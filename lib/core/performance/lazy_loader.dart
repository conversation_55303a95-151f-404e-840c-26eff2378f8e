import 'package:flutter/material.dart';
import 'package:get/get.dart';

/// Lazy<PERSON>oader class for implementing lazy loading functionality
/// Following Single Responsibility Principle by focusing only on lazy loading
class LazyLoader {
  // Private constructor to prevent instantiation
  LazyLoader._();

  /// Create a lazy-loaded widget that loads content when needed
  static Widget createLazyWidget({
    required Widget Function() builder,
    Widget? placeholder,
    Duration delay = const Duration(milliseconds: 100),
  }) {
    return _LazyWidget(
      builder: builder,
      placeholder: placeholder,
      delay: delay,
    );
  }

  /// Create a lazy-loaded page that loads content when navigated to
  static Widget createLazyPage({
    required Widget Function() pageBuilder,
    Widget? loadingWidget,
    String? loadingText,
  }) {
    return _LazyPage(
      pageBuilder: pageBuilder,
      loadingWidget: loadingWidget,
      loadingText: loadingText,
    );
  }

  /// Create a lazy-loaded list item
  static Widget createLazyListItem({
    required Widget Function() itemBuilder,
    Widget? placeholder,
    bool preload = false,
  }) {
    return _LazyListItem(
      itemBuilder: itemBuilder,
      placeholder: placeholder,
      preload: preload,
    );
  }

  /// Create a lazy-loaded image
  static Widget createLazyImage({
    required String imageUrl,
    Widget? placeholder,
    Widget? errorWidget,
    BoxFit fit = BoxFit.cover,
    double? width,
    double? height,
  }) {
    return _LazyImage(
      imageUrl: imageUrl,
      placeholder: placeholder,
      errorWidget: errorWidget,
      fit: fit,
      width: width,
      height: height,
    );
  }

  /// Create a lazy-loaded data widget
  static Widget createLazyData<T>({
    required Future<T> Function() dataLoader,
    required Widget Function(T data) builder,
    Widget? loadingWidget,
    Widget Function(Object error)? errorBuilder,
  }) {
    return _LazyData<T>(
      dataLoader: dataLoader,
      builder: builder,
      loadingWidget: loadingWidget,
      errorBuilder: errorBuilder,
    );
  }
}

/// Internal lazy widget implementation
class _LazyWidget extends StatefulWidget {
  final Widget Function() builder;
  final Widget? placeholder;
  final Duration delay;

  const _LazyWidget({
    required this.builder,
    this.placeholder,
    required this.delay,
  });

  @override
  State<_LazyWidget> createState() => _LazyWidgetState();
}

class _LazyWidgetState extends State<_LazyWidget> {
  bool _isLoaded = false;
  Widget? _content;

  @override
  void initState() {
    super.initState();
    _loadContent();
  }

  void _loadContent() async {
    await Future.delayed(widget.delay);
    if (mounted) {
      setState(() {
        _content = widget.builder();
        _isLoaded = true;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoaded && _content != null) {
      return _content!;
    }

    return widget.placeholder ?? const SizedBox.shrink();
  }
}

/// Internal lazy page implementation
class _LazyPage extends StatefulWidget {
  final Widget Function() pageBuilder;
  final Widget? loadingWidget;
  final String? loadingText;

  const _LazyPage({
    required this.pageBuilder,
    this.loadingWidget,
    this.loadingText,
  });

  @override
  State<_LazyPage> createState() => _LazyPageState();
}

class _LazyPageState extends State<_LazyPage> {
  bool _isLoaded = false;
  Widget? _page;

  @override
  void initState() {
    super.initState();
    _loadPage();
  }

  void _loadPage() async {
    // Simulate loading delay for better UX
    await Future.delayed(const Duration(milliseconds: 50));
    
    if (mounted) {
      setState(() {
        _page = widget.pageBuilder();
        _isLoaded = true;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoaded && _page != null) {
      return _page!;
    }

    return widget.loadingWidget ?? _buildDefaultLoading();
  }

  Widget _buildDefaultLoading() {
    return Scaffold(
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const CircularProgressIndicator(),
            if (widget.loadingText != null) ...[
              const SizedBox(height: 16),
              Text(
                widget.loadingText!,
                style: const TextStyle(fontSize: 16),
              ),
            ],
          ],
        ),
      ),
    );
  }
}

/// Internal lazy list item implementation
class _LazyListItem extends StatefulWidget {
  final Widget Function() itemBuilder;
  final Widget? placeholder;
  final bool preload;

  const _LazyListItem({
    required this.itemBuilder,
    this.placeholder,
    required this.preload,
  });

  @override
  State<_LazyListItem> createState() => _LazyListItemState();
}

class _LazyListItemState extends State<_LazyListItem> {
  bool _isVisible = false;
  bool _isLoaded = false;
  Widget? _item;

  @override
  void initState() {
    super.initState();
    if (widget.preload) {
      _loadItem();
    }
  }

  void _loadItem() async {
    if (!_isLoaded) {
      setState(() {
        _item = widget.itemBuilder();
        _isLoaded = true;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return VisibilityDetector(
      key: ValueKey(hashCode),
      onVisibilityChanged: (info) {
        if (info.visibleFraction > 0 && !_isVisible) {
          _isVisible = true;
          _loadItem();
        }
      },
      child: _isLoaded && _item != null
          ? _item!
          : widget.placeholder ?? const SizedBox(height: 50),
    );
  }
}

/// Internal lazy image implementation
class _LazyImage extends StatefulWidget {
  final String imageUrl;
  final Widget? placeholder;
  final Widget? errorWidget;
  final BoxFit fit;
  final double? width;
  final double? height;

  const _LazyImage({
    required this.imageUrl,
    this.placeholder,
    this.errorWidget,
    required this.fit,
    this.width,
    this.height,
  });

  @override
  State<_LazyImage> createState() => _LazyImageState();
}

class _LazyImageState extends State<_LazyImage> {
  bool _isVisible = false;

  @override
  Widget build(BuildContext context) {
    return VisibilityDetector(
      key: ValueKey(widget.imageUrl),
      onVisibilityChanged: (info) {
        if (info.visibleFraction > 0 && !_isVisible) {
          setState(() {
            _isVisible = true;
          });
        }
      },
      child: _isVisible
          ? Image.network(
              widget.imageUrl,
              fit: widget.fit,
              width: widget.width,
              height: widget.height,
              loadingBuilder: (context, child, loadingProgress) {
                if (loadingProgress == null) return child;
                return widget.placeholder ?? 
                    const Center(child: CircularProgressIndicator());
              },
              errorBuilder: (context, error, stackTrace) {
                return widget.errorWidget ?? 
                    const Icon(Icons.error_outline);
              },
            )
          : widget.placeholder ?? 
              Container(
                width: widget.width,
                height: widget.height,
                color: Colors.grey.shade200,
              ),
    );
  }
}

/// Internal lazy data implementation
class _LazyData<T> extends StatefulWidget {
  final Future<T> Function() dataLoader;
  final Widget Function(T data) builder;
  final Widget? loadingWidget;
  final Widget Function(Object error)? errorBuilder;

  const _LazyData({
    required this.dataLoader,
    required this.builder,
    this.loadingWidget,
    this.errorBuilder,
  });

  @override
  State<_LazyData<T>> createState() => _LazyDataState<T>();
}

class _LazyDataState<T> extends State<_LazyData<T>> {
  bool _isLoading = true;
  T? _data;
  Object? _error;

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  void _loadData() async {
    try {
      final data = await widget.dataLoader();
      if (mounted) {
        setState(() {
          _data = data;
          _isLoading = false;
        });
      }
    } catch (error) {
      if (mounted) {
        setState(() {
          _error = error;
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return widget.loadingWidget ?? 
          const Center(child: CircularProgressIndicator());
    }

    if (_error != null) {
      return widget.errorBuilder?.call(_error!) ?? 
          Center(child: Text('Error: $_error'));
    }

    if (_data != null) {
      return widget.builder(_data!);
    }

    return const SizedBox.shrink();
  }
}

/// Visibility detector for lazy loading
class VisibilityDetector extends StatefulWidget {
  final Key key;
  final Widget child;
  final Function(VisibilityInfo) onVisibilityChanged;

  const VisibilityDetector({
    required this.key,
    required this.child,
    required this.onVisibilityChanged,
  }) : super(key: key);

  @override
  State<VisibilityDetector> createState() => _VisibilityDetectorState();
}

class _VisibilityDetectorState extends State<VisibilityDetector> {
  @override
  Widget build(BuildContext context) {
    // Simplified visibility detection
    // In a real implementation, you would use a proper visibility detection library
    WidgetsBinding.instance.addPostFrameCallback((_) {
      widget.onVisibilityChanged(VisibilityInfo(visibleFraction: 1.0));
    });
    
    return widget.child;
  }
}

/// Visibility info class
class VisibilityInfo {
  final double visibleFraction;

  VisibilityInfo({required this.visibleFraction});
}
