import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// CacheManager class for managing application cache
/// Following Single Responsibility Principle by focusing only on cache management
class CacheManager {
  static CacheManager? _instance;
  static SharedPreferences? _prefs;

  // Private constructor
  CacheManager._();

  /// Get singleton instance
  static CacheManager get instance {
    _instance ??= CacheManager._();
    return _instance!;
  }

  /// Initialize cache manager
  static Future<void> init() async {
    _prefs = await SharedPreferences.getInstance();
  }

  /// Cache expiration times
  static const Duration _defaultExpiration = Duration(hours: 1);
  static const Duration _shortExpiration = Duration(minutes: 15);
  static const Duration _longExpiration = Duration(days: 1);

  /// Cache a string value
  Future<bool> cacheString(
    String key,
    String value, {
    Duration? expiration,
  }) async {
    if (_prefs == null) await init();

    final cacheData = {
      'value': value,
      'timestamp': DateTime.now().millisecondsSinceEpoch,
      'expiration': (expiration ?? _defaultExpiration).inMilliseconds,
    };

    return await _prefs!.setString(key, jsonEncode(cacheData));
  }

  /// Get cached string value
  String? getCachedString(String key) {
    if (_prefs == null) return null;

    final cachedData = _prefs!.getString(key);
    if (cachedData == null) return null;

    try {
      final data = jsonDecode(cachedData) as Map<String, dynamic>;
      final timestamp = data['timestamp'] as int;
      final expiration = data['expiration'] as int;
      final value = data['value'] as String;

      // Check if cache is expired
      final now = DateTime.now().millisecondsSinceEpoch;
      if (now - timestamp > expiration) {
        _prefs!.remove(key);
        return null;
      }

      return value;
    } catch (e) {
      debugPrint('Error reading cached string: $e');
      _prefs!.remove(key);
      return null;
    }
  }

  /// Cache a JSON object
  Future<bool> cacheJson(
    String key,
    Map<String, dynamic> value, {
    Duration? expiration,
  }) async {
    return await cacheString(key, jsonEncode(value), expiration: expiration);
  }

  /// Get cached JSON object
  Map<String, dynamic>? getCachedJson(String key) {
    final cachedString = getCachedString(key);
    if (cachedString == null) return null;

    try {
      return jsonDecode(cachedString) as Map<String, dynamic>;
    } catch (e) {
      debugPrint('Error parsing cached JSON: $e');
      _prefs!.remove(key);
      return null;
    }
  }

  /// Cache a list
  Future<bool> cacheList(
    String key,
    List<dynamic> value, {
    Duration? expiration,
  }) async {
    return await cacheString(key, jsonEncode(value), expiration: expiration);
  }

  /// Get cached list
  List<dynamic>? getCachedList(String key) {
    final cachedString = getCachedString(key);
    if (cachedString == null) return null;

    try {
      return jsonDecode(cachedString) as List<dynamic>;
    } catch (e) {
      debugPrint('Error parsing cached list: $e');
      _prefs!.remove(key);
      return null;
    }
  }

  /// Cache with automatic type detection
  Future<bool> cache<T>(String key, T value, {Duration? expiration}) async {
    if (value is String) {
      return await cacheString(key, value, expiration: expiration);
    } else if (value is Map<String, dynamic>) {
      return await cacheJson(key, value, expiration: expiration);
    } else if (value is List) {
      return await cacheList(key, value, expiration: expiration);
    } else {
      // Convert to JSON string for other types
      return await cacheString(key, jsonEncode(value), expiration: expiration);
    }
  }

  /// Get cached value with automatic type detection
  T? getCached<T>(String key) {
    if (T == String) {
      return getCachedString(key) as T?;
    } else if (T == Map<String, dynamic>) {
      return getCachedJson(key) as T?;
    } else if (T == List<dynamic>) {
      return getCachedList(key) as T?;
    } else {
      final cachedString = getCachedString(key);
      if (cachedString == null) return null;

      try {
        return jsonDecode(cachedString) as T;
      } catch (e) {
        debugPrint('Error parsing cached value: $e');
        return null;
      }
    }
  }

  /// Check if key exists and is not expired
  bool isCached(String key) {
    return getCachedString(key) != null;
  }

  /// Remove specific cache entry
  Future<bool> remove(String key) async {
    if (_prefs == null) await init();
    return await _prefs!.remove(key);
  }

  /// Clear all cache
  Future<bool> clearAll() async {
    if (_prefs == null) await init();
    return await _prefs!.clear();
  }

  /// Clear expired cache entries
  Future<void> clearExpired() async {
    if (_prefs == null) await init();

    final keys = _prefs!.getKeys();
    final keysToRemove = <String>[];

    for (final key in keys) {
      // Only process keys that start with our cache prefix to avoid type errors
      if (!key.startsWith('cache_')) continue;

      final cachedData = _prefs!.getString(key);
      if (cachedData == null) continue;

      try {
        final data = jsonDecode(cachedData) as Map<String, dynamic>;
        final timestamp = data['timestamp'] as int;
        final expiration = data['expiration'] as int;

        final now = DateTime.now().millisecondsSinceEpoch;
        if (now - timestamp > expiration) {
          keysToRemove.add(key);
        }
      } catch (e) {
        // Invalid cache entry, mark for removal
        keysToRemove.add(key);
      }
    }

    for (final key in keysToRemove) {
      await _prefs!.remove(key);
    }
  }

  /// Get cache size (number of entries)
  int getCacheSize() {
    if (_prefs == null) return 0;
    return _prefs!.getKeys().length;
  }

  /// Cache with short expiration (15 minutes)
  Future<bool> cacheShort<T>(String key, T value) async {
    return await cache(key, value, expiration: _shortExpiration);
  }

  /// Cache with long expiration (1 day)
  Future<bool> cacheLong<T>(String key, T value) async {
    return await cache(key, value, expiration: _longExpiration);
  }

  /// Cache API response
  Future<bool> cacheApiResponse(
    String endpoint,
    Map<String, dynamic> response, {
    Duration? expiration,
  }) async {
    final key = 'api_$endpoint';
    return await cacheJson(key, response, expiration: expiration);
  }

  /// Get cached API response
  Map<String, dynamic>? getCachedApiResponse(String endpoint) {
    final key = 'api_$endpoint';
    return getCachedJson(key);
  }

  /// Cache user data
  Future<bool> cacheUserData(
    String userId,
    Map<String, dynamic> userData,
  ) async {
    final key = 'user_$userId';
    return await cacheJson(key, userData, expiration: _longExpiration);
  }

  /// Get cached user data
  Map<String, dynamic>? getCachedUserData(String userId) {
    final key = 'user_$userId';
    return getCachedJson(key);
  }

  /// Cache search results
  Future<bool> cacheSearchResults(String query, List<dynamic> results) async {
    final key = 'search_${query.hashCode}';
    return await cacheList(key, results, expiration: _shortExpiration);
  }

  /// Get cached search results
  List<dynamic>? getCachedSearchResults(String query) {
    final key = 'search_${query.hashCode}';
    return getCachedList(key);
  }

  /// Cache image metadata
  Future<bool> cacheImageMetadata(
    String imageUrl,
    Map<String, dynamic> metadata,
  ) async {
    final key = 'image_${imageUrl.hashCode}';
    return await cacheJson(key, metadata, expiration: _longExpiration);
  }

  /// Get cached image metadata
  Map<String, dynamic>? getCachedImageMetadata(String imageUrl) {
    final key = 'image_${imageUrl.hashCode}';
    return getCachedJson(key);
  }

  /// Cache with custom key prefix
  Future<bool> cacheWithPrefix<T>(
    String prefix,
    String key,
    T value, {
    Duration? expiration,
  }) async {
    final fullKey = '${prefix}_$key';
    return await cache(fullKey, value, expiration: expiration);
  }

  /// Get cached value with prefix
  T? getCachedWithPrefix<T>(String prefix, String key) {
    final fullKey = '${prefix}_$key';
    return getCached<T>(fullKey);
  }

  /// Remove all cache entries with specific prefix
  Future<void> clearPrefix(String prefix) async {
    if (_prefs == null) await init();

    final keys = _prefs!.getKeys();
    final keysToRemove = keys.where((key) => key.startsWith('${prefix}_'));

    for (final key in keysToRemove) {
      await _prefs!.remove(key);
    }
  }

  /// Get cache statistics
  Map<String, dynamic> getCacheStats() {
    if (_prefs == null) return {};

    final keys = _prefs!.getKeys();
    int validEntries = 0;
    int expiredEntries = 0;
    int invalidEntries = 0;

    for (final key in keys) {
      final cachedData = _prefs!.getString(key);
      if (cachedData == null) {
        invalidEntries++;
        continue;
      }

      try {
        final data = jsonDecode(cachedData) as Map<String, dynamic>;
        final timestamp = data['timestamp'] as int;
        final expiration = data['expiration'] as int;

        final now = DateTime.now().millisecondsSinceEpoch;
        if (now - timestamp > expiration) {
          expiredEntries++;
        } else {
          validEntries++;
        }
      } catch (e) {
        invalidEntries++;
      }
    }

    return {
      'totalEntries': keys.length,
      'validEntries': validEntries,
      'expiredEntries': expiredEntries,
      'invalidEntries': invalidEntries,
    };
  }
}
