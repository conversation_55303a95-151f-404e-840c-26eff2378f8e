import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

/// PerformanceMonitor class for monitoring app performance
/// Following Single Responsibility Principle by focusing only on performance monitoring
class PerformanceMonitor {
  static PerformanceMonitor? _instance;
  static final Map<String, DateTime> _startTimes = {};
  static final Map<String, Duration> _durations = {};
  static final List<PerformanceMetric> _metrics = [];

  // Private constructor
  PerformanceMonitor._();

  /// Get singleton instance
  static PerformanceMonitor get instance {
    _instance ??= PerformanceMonitor._();
    return _instance!;
  }

  /// Start timing an operation
  static void startTimer(String operationName) {
    _startTimes[operationName] = DateTime.now();
    if (kDebugMode) {
      debugPrint('⏱️ Started timing: $operationName');
    }
  }

  /// End timing an operation
  static Duration? endTimer(String operationName) {
    final startTime = _startTimes[operationName];
    if (startTime == null) {
      if (kDebugMode) {
        debugPrint('⚠️ No start time found for: $operationName');
      }
      return null;
    }

    final duration = DateTime.now().difference(startTime);
    _durations[operationName] = duration;
    _startTimes.remove(operationName);

    // Add to metrics
    _metrics.add(PerformanceMetric(
      operationName: operationName,
      duration: duration,
      timestamp: DateTime.now(),
    ));

    if (kDebugMode) {
      debugPrint('⏱️ Completed: $operationName in ${duration.inMilliseconds}ms');
    }

    // Keep only last 100 metrics
    if (_metrics.length > 100) {
      _metrics.removeAt(0);
    }

    return duration;
  }

  /// Time a function execution
  static Future<T> timeFunction<T>(
    String operationName,
    Future<T> Function() function,
  ) async {
    startTimer(operationName);
    try {
      final result = await function();
      endTimer(operationName);
      return result;
    } catch (e) {
      endTimer(operationName);
      rethrow;
    }
  }

  /// Time a synchronous function execution
  static T timeFunctionSync<T>(
    String operationName,
    T Function() function,
  ) {
    startTimer(operationName);
    try {
      final result = function();
      endTimer(operationName);
      return result;
    } catch (e) {
      endTimer(operationName);
      rethrow;
    }
  }

  /// Monitor widget build performance
  static Widget monitorWidgetBuild(
    String widgetName,
    Widget Function() builder,
  ) {
    return _PerformanceMonitorWidget(
      widgetName: widgetName,
      builder: builder,
    );
  }

  /// Monitor page navigation performance
  static void monitorNavigation(String fromPage, String toPage) {
    final operationName = 'Navigation: $fromPage -> $toPage';
    startTimer(operationName);
    
    // End timer after next frame
    WidgetsBinding.instance.addPostFrameCallback((_) {
      endTimer(operationName);
    });
  }

  /// Monitor API call performance
  static Future<T> monitorApiCall<T>(
    String apiEndpoint,
    Future<T> Function() apiCall,
  ) async {
    return await timeFunction('API: $apiEndpoint', apiCall);
  }

  /// Monitor database operation performance
  static Future<T> monitorDatabaseOperation<T>(
    String operation,
    Future<T> Function() dbOperation,
  ) async {
    return await timeFunction('DB: $operation', dbOperation);
  }

  /// Get performance metrics
  static List<PerformanceMetric> getMetrics() {
    return List.unmodifiable(_metrics);
  }

  /// Get metrics for specific operation
  static List<PerformanceMetric> getMetricsForOperation(String operationName) {
    return _metrics.where((m) => m.operationName == operationName).toList();
  }

  /// Get average duration for operation
  static Duration? getAverageDuration(String operationName) {
    final operationMetrics = getMetricsForOperation(operationName);
    if (operationMetrics.isEmpty) return null;

    final totalMs = operationMetrics
        .map((m) => m.duration.inMilliseconds)
        .reduce((a, b) => a + b);
    
    return Duration(milliseconds: totalMs ~/ operationMetrics.length);
  }

  /// Get performance summary
  static Map<String, dynamic> getPerformanceSummary() {
    final summary = <String, dynamic>{};
    final operationNames = _metrics.map((m) => m.operationName).toSet();

    for (final operationName in operationNames) {
      final operationMetrics = getMetricsForOperation(operationName);
      final durations = operationMetrics.map((m) => m.duration.inMilliseconds);
      
      if (durations.isNotEmpty) {
        summary[operationName] = {
          'count': durations.length,
          'averageMs': durations.reduce((a, b) => a + b) / durations.length,
          'minMs': durations.reduce((a, b) => a < b ? a : b),
          'maxMs': durations.reduce((a, b) => a > b ? a : b),
          'lastMs': durations.last,
        };
      }
    }

    return summary;
  }

  /// Clear all metrics
  static void clearMetrics() {
    _metrics.clear();
    _durations.clear();
    _startTimes.clear();
  }

  /// Log performance summary
  static void logPerformanceSummary() {
    if (!kDebugMode) return;

    final summary = getPerformanceSummary();
    debugPrint('📊 Performance Summary:');
    
    for (final entry in summary.entries) {
      final stats = entry.value as Map<String, dynamic>;
      debugPrint(
        '  ${entry.key}: avg=${stats['averageMs']?.toStringAsFixed(1)}ms, '
        'min=${stats['minMs']}ms, max=${stats['maxMs']}ms, count=${stats['count']}',
      );
    }
  }

  /// Monitor memory usage
  static void monitorMemoryUsage(String context) {
    if (!kDebugMode) return;

    // This is a simplified memory monitoring
    // In a real app, you might use more sophisticated memory profiling
    final info = ProcessInfo.currentRss;
    debugPrint('🧠 Memory usage at $context: ${info ~/ 1024 ~/ 1024}MB');
  }

  /// Monitor frame rate
  static void startFrameRateMonitoring() {
    if (!kDebugMode) return;

    WidgetsBinding.instance.addTimingsCallback((timings) {
      for (final timing in timings) {
        final frameTime = timing.totalSpan.inMilliseconds;
        if (frameTime > 16) { // More than 16ms indicates dropped frames
          debugPrint('🎬 Slow frame detected: ${frameTime}ms');
        }
      }
    });
  }

  /// Check if operation is slow
  static bool isOperationSlow(String operationName, {int thresholdMs = 1000}) {
    final avgDuration = getAverageDuration(operationName);
    return avgDuration != null && avgDuration.inMilliseconds > thresholdMs;
  }

  /// Get slow operations
  static List<String> getSlowOperations({int thresholdMs = 1000}) {
    final operationNames = _metrics.map((m) => m.operationName).toSet();
    return operationNames
        .where((name) => isOperationSlow(name, thresholdMs: thresholdMs))
        .toList();
  }
}

/// Performance metric data class
class PerformanceMetric {
  final String operationName;
  final Duration duration;
  final DateTime timestamp;

  const PerformanceMetric({
    required this.operationName,
    required this.duration,
    required this.timestamp,
  });

  @override
  String toString() {
    return 'PerformanceMetric(operation: $operationName, duration: ${duration.inMilliseconds}ms, timestamp: $timestamp)';
  }
}

/// Widget for monitoring build performance
class _PerformanceMonitorWidget extends StatelessWidget {
  final String widgetName;
  final Widget Function() builder;

  const _PerformanceMonitorWidget({
    required this.widgetName,
    required this.builder,
  });

  @override
  Widget build(BuildContext context) {
    return PerformanceMonitor.timeFunctionSync(
      'Widget Build: $widgetName',
      builder,
    );
  }
}

/// Extension for easy performance monitoring
extension PerformanceMonitorExtension on Widget {
  /// Monitor this widget's build performance
  Widget monitorPerformance(String widgetName) {
    return PerformanceMonitor.monitorWidgetBuild(widgetName, () => this);
  }
}

/// Extension for Future performance monitoring
extension FuturePerformanceExtension<T> on Future<T> {
  /// Monitor this future's execution time
  Future<T> monitorPerformance(String operationName) {
    return PerformanceMonitor.timeFunction(operationName, () => this);
  }
}
