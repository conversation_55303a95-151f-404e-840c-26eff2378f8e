import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'cache_manager.dart';

/// ImageOptimizer class for optimizing image loading and caching
/// Following Single Responsibility Principle by focusing only on image optimization
class ImageOptimizer {
  // Private constructor to prevent instantiation
  ImageOptimizer._();

  /// Create an optimized network image
  static Widget createOptimizedImage({
    required String imageUrl,
    double? width,
    double? height,
    BoxFit fit = BoxFit.cover,
    Widget? placeholder,
    Widget? errorWidget,
    bool enableMemoryCache = true,
    bool enableDiskCache = true,
    Duration? cacheExpiration,
    String? semanticLabel,
  }) {
    return _OptimizedImage(
      imageUrl: imageUrl,
      width: width,
      height: height,
      fit: fit,
      placeholder: placeholder,
      errorWidget: errorWidget,
      enableMemoryCache: enableMemoryCache,
      enableDiskCache: enableDiskCache,
      cacheExpiration: cacheExpiration,
      semanticLabel: semanticLabel,
    );
  }

  /// Create an optimized avatar image
  static Widget createOptimizedAvatar({
    required String imageUrl,
    required double radius,
    Widget? placeholder,
    Widget? errorWidget,
    String? semanticLabel,
  }) {
    return _OptimizedAvatar(
      imageUrl: imageUrl,
      radius: radius,
      placeholder: placeholder,
      errorWidget: errorWidget,
      semanticLabel: semanticLabel,
    );
  }

  /// Create an optimized thumbnail
  static Widget createOptimizedThumbnail({
    required String imageUrl,
    required double size,
    BoxFit fit = BoxFit.cover,
    Widget? placeholder,
    Widget? errorWidget,
    String? semanticLabel,
  }) {
    return _OptimizedThumbnail(
      imageUrl: imageUrl,
      size: size,
      fit: fit,
      placeholder: placeholder,
      errorWidget: errorWidget,
      semanticLabel: semanticLabel,
    );
  }

  /// Create an optimized gallery image
  static Widget createOptimizedGalleryImage({
    required String imageUrl,
    required double width,
    required double height,
    BoxFit fit = BoxFit.cover,
    Widget? placeholder,
    Widget? errorWidget,
    VoidCallback? onTap,
    String? semanticLabel,
  }) {
    return _OptimizedGalleryImage(
      imageUrl: imageUrl,
      width: width,
      height: height,
      fit: fit,
      placeholder: placeholder,
      errorWidget: errorWidget,
      onTap: onTap,
      semanticLabel: semanticLabel,
    );
  }

  /// Preload images for better performance
  static Future<void> preloadImages(
    BuildContext context,
    List<String> imageUrls,
  ) async {
    for (final url in imageUrls) {
      try {
        await precacheImage(CachedNetworkImageProvider(url), context);
      } catch (e) {
        debugPrint('Failed to preload image: $url, Error: $e');
      }
    }
  }

  /// Clear image cache
  static Future<void> clearImageCache() async {
    await CachedNetworkImage.evictFromCache('');
  }

  /// Get optimized image URL with parameters
  static String getOptimizedImageUrl(
    String baseUrl, {
    int? width,
    int? height,
    int? quality,
    String? format,
  }) {
    final uri = Uri.parse(baseUrl);
    final queryParams = Map<String, String>.from(uri.queryParameters);

    if (width != null) queryParams['w'] = width.toString();
    if (height != null) queryParams['h'] = height.toString();
    if (quality != null) queryParams['q'] = quality.toString();
    if (format != null) queryParams['f'] = format;

    return uri.replace(queryParameters: queryParams).toString();
  }
}

/// Internal optimized image implementation
class _OptimizedImage extends StatelessWidget {
  final String imageUrl;
  final double? width;
  final double? height;
  final BoxFit fit;
  final Widget? placeholder;
  final Widget? errorWidget;
  final bool enableMemoryCache;
  final bool enableDiskCache;
  final Duration? cacheExpiration;
  final String? semanticLabel;

  const _OptimizedImage({
    required this.imageUrl,
    this.width,
    this.height,
    required this.fit,
    this.placeholder,
    this.errorWidget,
    required this.enableMemoryCache,
    required this.enableDiskCache,
    this.cacheExpiration,
    this.semanticLabel,
  });

  @override
  Widget build(BuildContext context) {
    return Semantics(
      label: semanticLabel ?? 'صورة',
      image: true,
      child: CachedNetworkImage(
        imageUrl: imageUrl,
        width: width,
        height: height,
        fit: fit,
        memCacheWidth: width?.toInt(),
        memCacheHeight: height?.toInt(),
        placeholder:
            (context, url) => placeholder ?? _buildDefaultPlaceholder(),
        errorWidget:
            (context, url, error) => errorWidget ?? _buildDefaultError(),
        fadeInDuration: const Duration(milliseconds: 300),
        fadeOutDuration: const Duration(milliseconds: 100),
      ),
    );
  }

  Widget _buildDefaultPlaceholder() {
    return Container(
      width: width,
      height: height,
      color: Colors.grey.shade200,
      child: const Center(child: CircularProgressIndicator(strokeWidth: 2)),
    );
  }

  Widget _buildDefaultError() {
    return Container(
      width: width,
      height: height,
      color: Colors.grey.shade200,
      child: const Icon(Icons.error_outline, color: Colors.grey),
    );
  }
}

/// Internal optimized avatar implementation
class _OptimizedAvatar extends StatelessWidget {
  final String imageUrl;
  final double radius;
  final Widget? placeholder;
  final Widget? errorWidget;
  final String? semanticLabel;

  const _OptimizedAvatar({
    required this.imageUrl,
    required this.radius,
    this.placeholder,
    this.errorWidget,
    this.semanticLabel,
  });

  @override
  Widget build(BuildContext context) {
    return Semantics(
      label: semanticLabel ?? 'صورة شخصية',
      image: true,
      child: CircleAvatar(
        radius: radius,
        backgroundColor: Colors.grey.shade200,
        child: ClipOval(
          child: CachedNetworkImage(
            imageUrl: imageUrl,
            width: radius * 2,
            height: radius * 2,
            fit: BoxFit.cover,
            memCacheWidth: (radius * 2).toInt(),
            memCacheHeight: (radius * 2).toInt(),
            placeholder:
                (context, url) => placeholder ?? _buildDefaultPlaceholder(),
            errorWidget:
                (context, url, error) => errorWidget ?? _buildDefaultError(),
          ),
        ),
      ),
    );
  }

  Widget _buildDefaultPlaceholder() {
    return Icon(Icons.person, size: radius * 0.8, color: Colors.grey);
  }

  Widget _buildDefaultError() {
    return Icon(Icons.person, size: radius * 0.8, color: Colors.grey);
  }
}

/// Internal optimized thumbnail implementation
class _OptimizedThumbnail extends StatelessWidget {
  final String imageUrl;
  final double size;
  final BoxFit fit;
  final Widget? placeholder;
  final Widget? errorWidget;
  final String? semanticLabel;

  const _OptimizedThumbnail({
    required this.imageUrl,
    required this.size,
    required this.fit,
    this.placeholder,
    this.errorWidget,
    this.semanticLabel,
  });

  @override
  Widget build(BuildContext context) {
    return Semantics(
      label: semanticLabel ?? 'صورة مصغرة',
      image: true,
      child: Container(
        width: size,
        height: size,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8),
          color: Colors.grey.shade200,
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(8),
          child: CachedNetworkImage(
            imageUrl: imageUrl,
            width: size,
            height: size,
            fit: fit,
            memCacheWidth: size.toInt(),
            memCacheHeight: size.toInt(),
            placeholder:
                (context, url) => placeholder ?? _buildDefaultPlaceholder(),
            errorWidget:
                (context, url, error) => errorWidget ?? _buildDefaultError(),
          ),
        ),
      ),
    );
  }

  Widget _buildDefaultPlaceholder() {
    return const Center(child: CircularProgressIndicator(strokeWidth: 2));
  }

  Widget _buildDefaultError() {
    return const Icon(Icons.image_not_supported_outlined, color: Colors.grey);
  }
}

/// Internal optimized gallery image implementation
class _OptimizedGalleryImage extends StatelessWidget {
  final String imageUrl;
  final double width;
  final double height;
  final BoxFit fit;
  final Widget? placeholder;
  final Widget? errorWidget;
  final VoidCallback? onTap;
  final String? semanticLabel;

  const _OptimizedGalleryImage({
    required this.imageUrl,
    required this.width,
    required this.height,
    required this.fit,
    this.placeholder,
    this.errorWidget,
    this.onTap,
    this.semanticLabel,
  });

  @override
  Widget build(BuildContext context) {
    return Semantics(
      label: semanticLabel ?? 'صورة في المعرض',
      image: true,
      button: onTap != null,
      child: GestureDetector(
        onTap: onTap,
        child: Container(
          width: width,
          height: height,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.1),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(12),
            child: CachedNetworkImage(
              imageUrl: imageUrl,
              width: width,
              height: height,
              fit: fit,
              memCacheWidth: width.toInt(),
              memCacheHeight: height.toInt(),
              placeholder:
                  (context, url) => placeholder ?? _buildDefaultPlaceholder(),
              errorWidget:
                  (context, url, error) => errorWidget ?? _buildDefaultError(),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildDefaultPlaceholder() {
    return Container(
      color: Colors.grey.shade200,
      child: const Center(child: CircularProgressIndicator(strokeWidth: 2)),
    );
  }

  Widget _buildDefaultError() {
    return Container(
      color: Colors.grey.shade200,
      child: const Icon(
        Icons.broken_image_outlined,
        color: Colors.grey,
        size: 48,
      ),
    );
  }
}
