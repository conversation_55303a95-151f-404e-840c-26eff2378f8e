import 'package:flutter/material.dart';
import 'package:flutter/semantics.dart';

/// AccessibilityHelper class for managing accessibility features
/// Following Single Responsibility Principle by focusing only on accessibility
class AccessibilityHelper {
  // Private constructor to prevent instantiation
  AccessibilityHelper._();

  /// Announce message to screen readers
  static void announce(BuildContext context, String message) {
    SemanticsService.announce(message, TextDirection.ltr);
  }

  /// Announce message in Arabic to screen readers
  static void announceArabic(BuildContext context, String message) {
    SemanticsService.announce(message, TextDirection.rtl);
  }

  /// Create semantic label for buttons
  static String createButtonLabel(String text, {String? hint}) {
    if (hint != null) {
      return '$text. $hint';
    }
    return '$text. زر';
  }

  /// Create semantic label for text fields
  static String createTextFieldLabel(String label, {bool required = false, String? hint}) {
    String semanticLabel = label;
    if (required) {
      semanticLabel += '. مطلوب';
    }
    if (hint != null) {
      semanticLabel += '. $hint';
    }
    return semanticLabel;
  }

  /// Create semantic label for navigation items
  static String createNavigationLabel(String text, {bool isSelected = false}) {
    String label = text;
    if (isSelected) {
      label += '. محدد حالياً';
    }
    return label;
  }

  /// Create semantic label for status indicators
  static String createStatusLabel(String status, String context) {
    return '$context: $status';
  }

  /// Create semantic label for data tables
  static String createTableCellLabel(String content, int row, int column, {String? columnHeader}) {
    String label = content;
    if (columnHeader != null) {
      label = '$columnHeader: $content';
    }
    label += '. صف ${row + 1}, عمود ${column + 1}';
    return label;
  }

  /// Create semantic label for progress indicators
  static String createProgressLabel(double progress, String context) {
    final percentage = (progress * 100).round();
    return '$context: $percentage بالمائة مكتمل';
  }

  /// Create semantic label for expandable items
  static String createExpandableLabel(String text, bool isExpanded) {
    String label = text;
    if (isExpanded) {
      label += '. موسع';
    } else {
      label += '. مطوي';
    }
    return label;
  }

  /// Create semantic label for toggle switches
  static String createToggleLabel(String text, bool isOn) {
    String label = text;
    if (isOn) {
      label += '. مفعل';
    } else {
      label += '. معطل';
    }
    return label;
  }

  /// Create semantic label for loading states
  static String createLoadingLabel(String context) {
    return '$context. جاري التحميل';
  }

  /// Create semantic label for error states
  static String createErrorLabel(String error, String context) {
    return 'خطأ في $context: $error';
  }

  /// Create semantic label for success states
  static String createSuccessLabel(String message, String context) {
    return 'نجح $context: $message';
  }

  /// Create semantic label for lists
  static String createListLabel(String itemText, int index, int total) {
    return '$itemText. عنصر ${index + 1} من $total';
  }

  /// Create semantic label for tabs
  static String createTabLabel(String text, int index, int total, bool isSelected) {
    String label = '$text. تبويب ${index + 1} من $total';
    if (isSelected) {
      label += '. محدد';
    }
    return label;
  }

  /// Create semantic label for modals/dialogs
  static String createModalLabel(String title, String type) {
    return '$type: $title';
  }

  /// Create semantic label for form validation
  static String createValidationLabel(String fieldName, String error) {
    return 'خطأ في $fieldName: $error';
  }

  /// Create semantic label for search results
  static String createSearchResultLabel(String query, int resultCount) {
    if (resultCount == 0) {
      return 'لا توجد نتائج للبحث عن: $query';
    } else if (resultCount == 1) {
      return 'نتيجة واحدة للبحث عن: $query';
    } else if (resultCount == 2) {
      return 'نتيجتان للبحث عن: $query';
    } else if (resultCount <= 10) {
      return '$resultCount نتائج للبحث عن: $query';
    } else {
      return '$resultCount نتيجة للبحث عن: $query';
    }
  }

  /// Create semantic label for pagination
  static String createPaginationLabel(int currentPage, int totalPages) {
    return 'صفحة $currentPage من $totalPages';
  }

  /// Create semantic label for sorting
  static String createSortLabel(String column, String direction) {
    String directionText = direction == 'asc' ? 'تصاعدي' : 'تنازلي';
    return 'مرتب حسب $column بشكل $directionText';
  }

  /// Create semantic label for filters
  static String createFilterLabel(String filterName, String value) {
    return 'مرشح $filterName: $value';
  }

  /// Create semantic label for date/time
  static String createDateTimeLabel(DateTime dateTime, {bool includeTime = false}) {
    // Format date in Arabic
    final months = [
      'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
      'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'
    ];
    
    String label = '${dateTime.day} ${months[dateTime.month - 1]} ${dateTime.year}';
    
    if (includeTime) {
      final hour = dateTime.hour > 12 ? dateTime.hour - 12 : dateTime.hour;
      final period = dateTime.hour >= 12 ? 'مساءً' : 'صباحاً';
      label += ' الساعة $hour:${dateTime.minute.toString().padLeft(2, '0')} $period';
    }
    
    return label;
  }

  /// Create semantic label for file uploads
  static String createFileUploadLabel(String fileName, String fileSize) {
    return 'ملف مرفوع: $fileName, الحجم: $fileSize';
  }

  /// Create semantic label for notifications
  static String createNotificationLabel(String message, String type, DateTime time) {
    final timeLabel = createDateTimeLabel(time, includeTime: true);
    return '$type: $message. $timeLabel';
  }

  /// Create semantic label for charts/graphs
  static String createChartLabel(String chartType, String title, String description) {
    return '$chartType بعنوان $title. $description';
  }

  /// Create semantic label for maps
  static String createMapLabel(String location, {String? additionalInfo}) {
    String label = 'خريطة تظهر $location';
    if (additionalInfo != null) {
      label += '. $additionalInfo';
    }
    return label;
  }

  /// Create semantic label for media controls
  static String createMediaControlLabel(String action, String mediaType) {
    return '$action $mediaType';
  }

  /// Create semantic label for rating/stars
  static String createRatingLabel(double rating, double maxRating) {
    return 'تقييم $rating من $maxRating نجوم';
  }

  /// Create semantic label for breadcrumbs
  static String createBreadcrumbLabel(List<String> breadcrumbs, int currentIndex) {
    String path = breadcrumbs.join(' > ');
    return 'مسار التنقل: $path. الموقع الحالي: ${breadcrumbs[currentIndex]}';
  }

  /// Create semantic label for drag and drop
  static String createDragDropLabel(String item, String action) {
    return '$item. $action';
  }

  /// Create semantic label for keyboard shortcuts
  static String createShortcutLabel(String action, String shortcut) {
    return '$action. اختصار لوحة المفاتيح: $shortcut';
  }

  /// Check if screen reader is enabled
  static bool isScreenReaderEnabled(BuildContext context) {
    return MediaQuery.of(context).accessibleNavigation;
  }

  /// Check if high contrast is enabled
  static bool isHighContrastEnabled(BuildContext context) {
    return MediaQuery.of(context).highContrast;
  }

  /// Check if bold text is enabled
  static bool isBoldTextEnabled(BuildContext context) {
    return MediaQuery.of(context).boldText;
  }

  /// Get text scale factor
  static double getTextScaleFactor(BuildContext context) {
    return MediaQuery.of(context).textScaleFactor;
  }

  /// Create focus node with semantic label
  static FocusNode createFocusNode({String? semanticLabel}) {
    final focusNode = FocusNode();
    if (semanticLabel != null) {
      focusNode.debugLabel = semanticLabel;
    }
    return focusNode;
  }

  /// Create semantic widget wrapper
  static Widget createSemanticWrapper({
    required Widget child,
    required String label,
    String? hint,
    String? value,
    bool? button,
    bool? header,
    bool? textField,
    bool? focusable,
    bool? enabled,
    VoidCallback? onTap,
  }) {
    return Semantics(
      label: label,
      hint: hint,
      value: value,
      button: button ?? false,
      header: header ?? false,
      textField: textField ?? false,
      focusable: focusable ?? true,
      enabled: enabled ?? true,
      onTap: onTap,
      child: child,
    );
  }
}
