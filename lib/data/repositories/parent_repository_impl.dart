import 'package:dartz/dartz.dart';
import '../../domain/entities/parent.dart';
import '../../domain/entities/student.dart';
import '../../domain/repositories/parent_repository.dart';
import '../../core/errors/failures.dart';
import '../../core/errors/exceptions.dart';
import '../../core/network/api_service.dart';
import '../../core/utils/logger.dart';
import '../models/parent_model.dart';
import '../models/student_response_model.dart';

/// Parent repository implementation
/// Following Single Responsibility Principle by focusing only on parent data operations
class ParentRepositoryImpl implements ParentRepository {
  final ApiService _apiService;

  ParentRepositoryImpl(this._apiService);

  @override
  Future<Either<Failure, List<Parent>>> getParents({
    int page = 1,
    int limit = 10,
    String? search,
  }) async {
    try {
      LoggerService.debug(
        'Fetching parents',
        data: {'page': page, 'limit': limit, 'search': search},
      );

      // Build URL with pagination and search (matching original SchoolX format)
      String url = 'parents/index?page=$page&limit=$limit';
      if (search != null && search.isNotEmpty) {
        url += '&text=$search';
      }

      final response = await _apiService.get(url, isAuth: true);

      final responseData = response.data;
      LoggerService.debug('Received parents response', data: responseData);

      // Check for API errors (matching original SchoolX format)
      if (responseData['errors'] == true) {
        final errorMessage =
            responseData['message'] ?? 'Failed to fetch parents';
        return Left(ServerFailure(message: errorMessage));
      }

      final parentsData = responseData['data']['data'] as List<dynamic>?;
      if (parentsData == null) {
        return const Right([]);
      }

      final parents =
          parentsData
              .map((json) => ParentModel.fromJson(json as Map<String, dynamic>))
              .toList();

      LoggerService.info('Successfully fetched ${parents.length} parents');
      return Right(parents);
    } on ServerException catch (e) {
      LoggerService.error('Server exception while fetching parents', error: e);
      return Left(ServerFailure(message: e.message));
    } catch (e) {
      LoggerService.error('Unexpected error while fetching parents', error: e);
      return Left(ServerFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, Parent>> getParentById(int id) async {
    try {
      LoggerService.debug('Fetching parent by ID', data: {'parentId': id});

      final response = await _apiService.get('parents/show/$id', isAuth: true);

      final responseData = response.data;
      LoggerService.debug('Received parent response', data: responseData);

      if (responseData['errors'] == true) {
        final errorMessage = responseData['message'] ?? 'Parent not found';
        return Left(ServerFailure(message: errorMessage));
      }

      final parentData = responseData['data'];
      if (parentData == null) {
        return Left(ServerFailure(message: 'Parent not found'));
      }

      final parent = ParentModel.fromJson(parentData as Map<String, dynamic>);
      LoggerService.info('Successfully fetched parent', data: {'parentId': id});
      return Right(parent);
    } on ServerException catch (e) {
      LoggerService.error('Server exception while fetching parent', error: e);
      return Left(ServerFailure(message: e.message));
    } catch (e) {
      LoggerService.error('Unexpected error while fetching parent', error: e);
      return Left(ServerFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, Parent>> createParent(Parent parent) async {
    try {
      LoggerService.debug('Creating parent', data: {'parentName': parent.name});

      // Prepare request data
      final requestData = {
        'name': parent.name,
        'email': parent.email,
        'phone': parent.phone,
        'address': parent.address,
        'status': parent.status ?? 1,
      };

      final response = await _apiService.post(
        url: 'parents/store',
        body: requestData,
        isAuth: true,
      );

      final responseData = response.data;
      LoggerService.debug(
        'Received create parent response',
        data: responseData,
      );

      if (responseData['errors'] == true) {
        final errorMessage =
            responseData['message'] ?? 'Failed to create parent';
        return Left(ServerFailure(message: errorMessage));
      }

      final createdParentData = responseData['data'];
      if (createdParentData == null) {
        return Left(ServerFailure(message: 'Failed to create parent'));
      }

      final createdParent = ParentModel.fromJson(
        createdParentData as Map<String, dynamic>,
      );
      LoggerService.info(
        'Successfully created parent',
        data: {'parentId': createdParent.id},
      );
      return Right(createdParent);
    } on ServerException catch (e) {
      LoggerService.error('Server exception while creating parent', error: e);
      return Left(ServerFailure(message: e.message));
    } catch (e) {
      LoggerService.error('Unexpected error while creating parent', error: e);
      return Left(ServerFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, Parent>> updateParent(Parent parent) async {
    try {
      LoggerService.debug('Updating parent', data: {'parentId': parent.id});

      // Prepare request data
      final requestData = {
        'name': parent.name,
        'email': parent.email,
        'phone': parent.phone,
        'address': parent.address,
        'status': parent.status ?? 1,
      };

      final response = await _apiService.post(
        url: 'parents/update/${parent.id}',
        body: requestData,
        isAuth: true,
      );

      final responseData = response.data;
      LoggerService.debug(
        'Received update parent response',
        data: responseData,
      );

      if (responseData['errors'] == true) {
        final errorMessage =
            responseData['message'] ?? 'Failed to update parent';
        return Left(ServerFailure(message: errorMessage));
      }

      final updatedParentData = responseData['data'];
      if (updatedParentData == null) {
        return Left(ServerFailure(message: 'Failed to update parent'));
      }

      final updatedParent = ParentModel.fromJson(
        updatedParentData as Map<String, dynamic>,
      );
      LoggerService.info(
        'Successfully updated parent',
        data: {'parentId': parent.id},
      );
      return Right(updatedParent);
    } on ServerException catch (e) {
      LoggerService.error('Server exception while updating parent', error: e);
      return Left(ServerFailure(message: e.message));
    } catch (e) {
      LoggerService.error('Unexpected error while updating parent', error: e);
      return Left(ServerFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, bool>> deleteParent(int id) async {
    try {
      LoggerService.debug('Deleting parent', data: {'parentId': id});

      final response = await _apiService.delete(
        url: 'parents/destroy/$id',
        body: {},
        isAuth: true,
      );

      final responseData = response.data;
      LoggerService.debug(
        'Received delete parent response',
        data: responseData,
      );

      if (responseData['errors'] == true) {
        final errorMessage =
            responseData['message'] ?? 'Failed to delete parent';
        LoggerService.error(
          'API returned error for parent deletion',
          data: {'parentId': id, 'error': errorMessage},
        );
        return Left(ServerFailure(message: errorMessage));
      }

      LoggerService.info('Successfully deleted parent', data: {'parentId': id});
      return const Right(true);
    } catch (e) {
      LoggerService.error('Server exception while deleting parent', error: e);
      return Left(ServerFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, List<Student>>> getStudentsByParentId(
    int parentId,
  ) async {
    try {
      LoggerService.debug(
        'Fetching students by parent ID',
        data: {'parentId': parentId},
      );

      final response = await _apiService.get(
        'parents/show/$parentId',
        isAuth: true,
      );

      final responseData = response.data;
      LoggerService.debug(
        'Received parent students response',
        data: responseData,
      );

      if (responseData['errors'] == true) {
        final errorMessage =
            responseData['message'] ?? 'Failed to fetch parent students';
        return Left(ServerFailure(message: errorMessage));
      }

      final parentData = responseData['data'];
      if (parentData == null) {
        return const Right([]);
      }

      final studentsData = parentData['children'] as List<dynamic>?;
      if (studentsData == null) {
        return const Right([]);
      }

      final students =
          studentsData
              .map(
                (json) => StudentModel.fromJson(json as Map<String, dynamic>),
              )
              .toList();

      LoggerService.info(
        'Successfully fetched ${students.length} students for parent',
      );
      return Right(students);
    } on ServerException catch (e) {
      LoggerService.error(
        'Server exception while fetching parent students',
        error: e,
      );
      return Left(ServerFailure(message: e.message));
    } catch (e) {
      LoggerService.error(
        'Unexpected error while fetching parent students',
        error: e,
      );
      return Left(ServerFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, bool>> addStudentToParent(
    int parentId,
    String studentId,
  ) async {
    try {
      LoggerService.debug(
        'Adding student to parent',
        data: {'parentId': parentId, 'studentId': studentId},
      );

      final response = await _apiService.post(
        url: 'parents/$parentId/students',
        body: {'student_id': studentId},
        isAuth: true,
      );

      final responseData = response.data;
      LoggerService.debug(
        'Received add student to parent response',
        data: responseData,
      );

      if (responseData['errors'] == true) {
        final errorMessage =
            responseData['message'] ?? 'Failed to add student to parent';
        return Left(ServerFailure(message: errorMessage));
      }

      LoggerService.info(
        'Successfully added student to parent',
        data: {'parentId': parentId, 'studentId': studentId},
      );
      return const Right(true);
    } catch (e) {
      LoggerService.error(
        'Server exception while adding student to parent',
        error: e,
      );
      return Left(ServerFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, bool>> removeStudentFromParent(
    int parentId,
    String studentId,
  ) async {
    try {
      LoggerService.debug(
        'Removing student from parent',
        data: {'parentId': parentId, 'studentId': studentId},
      );

      final response = await _apiService.delete(
        url: 'parents/$parentId/students/$studentId',
        body: {},
        isAuth: true,
      );

      final responseData = response.data;
      LoggerService.debug(
        'Received remove student from parent response',
        data: responseData,
      );

      if (responseData['errors'] == true) {
        final errorMessage =
            responseData['message'] ?? 'Failed to remove student from parent';
        return Left(ServerFailure(message: errorMessage));
      }

      LoggerService.info(
        'Successfully removed student from parent',
        data: {'parentId': parentId, 'studentId': studentId},
      );
      return const Right(true);
    } catch (e) {
      LoggerService.error(
        'Server exception while removing student from parent',
        error: e,
      );
      return Left(ServerFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, List<Parent>>> searchParents(
    String query, {
    int page = 1,
    int limit = 10,
  }) async {
    return getParents(page: page, limit: limit, search: query);
  }
}
