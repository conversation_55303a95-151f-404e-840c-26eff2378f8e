import '../../domain/entities/parent.dart';

/// Parent model for data layer
/// Following Single Responsibility Principle by focusing only on parent data mapping
class ParentModel extends Parent {
  const ParentModel({
    super.id,
    super.name,
    super.email,
    super.phone,
    super.emailVerifiedAt,
    super.address,
    super.status,
    super.logo,
    super.typeAuth,
    super.firebaseToken,
    super.createdAt,
    super.updatedAt,
    super.logoPath,
    super.subscriptionStatus,
    super.childrenIds,
    super.childrenCount,
  });

  factory ParentModel.fromJson(Map<String, dynamic> json) {
    return ParentModel(
      id: json['id'],
      name: json['name'],
      email: json['email'],
      phone: json['phone'],
      emailVerifiedAt: json['email_verified_at'],
      address: json['address'],
      status: json['status'],
      logo: json['logo'],
      typeAuth: json['typeAuth'],
      firebaseToken: json['firebase_token'],
      createdAt: json['created_at'],
      updatedAt: json['updated_at'],
      logoPath: json['logo_path'],
      subscriptionStatus: json['subscription_status'],
      childrenIds:
          json['children_ids'] != null
              ? List<String>.from(json['children_ids'])
              : null,
      childrenCount: json['children_count'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'email': email,
      'phone': phone,
      'email_verified_at': emailVerifiedAt,
      'address': address,
      'status': status,
      'logo': logo,
      'typeAuth': typeAuth,
      'firebase_token': firebaseToken,
      'created_at': createdAt,
      'updated_at': updatedAt,
      'logo_path': logoPath,
      'subscription_status': subscriptionStatus,
      'children_ids': childrenIds,
      'children_count': childrenCount,
    };
  }

  /// Convert to domain entity
  Parent toEntity() {
    return Parent(
      id: id,
      name: name,
      email: email,
      phone: phone,
      emailVerifiedAt: emailVerifiedAt,
      address: address,
      status: status,
      logo: logo,
      typeAuth: typeAuth,
      firebaseToken: firebaseToken,
      createdAt: createdAt,
      updatedAt: updatedAt,
      logoPath: logoPath,
      subscriptionStatus: subscriptionStatus,
      childrenIds: childrenIds,
      childrenCount: childrenCount,
    );
  }
}
