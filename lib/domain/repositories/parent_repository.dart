import 'package:dartz/dartz.dart';
import '../entities/parent.dart';
import '../entities/student.dart';
import '../../core/errors/failures.dart';

/// Parent repository interface
/// Following Single Responsibility Principle by focusing only on parent data operations
abstract class ParentRepository {
  /// Get all parents with pagination
  Future<Either<Failure, List<Parent>>> getParents({
    int page = 1,
    int limit = 10,
    String? search,
  });

  /// Get parent by ID
  Future<Either<Failure, Parent>> getParentById(int id);

  /// Create new parent
  Future<Either<Failure, Parent>> createParent(Parent parent);

  /// Update existing parent
  Future<Either<Failure, Parent>> updateParent(Parent parent);

  /// Delete parent
  Future<Either<Failure, bool>> deleteParent(int id);

  /// Get students by parent ID
  Future<Either<Failure, List<Student>>> getStudentsByParentId(int parentId);

  /// Add student to parent
  Future<Either<Failure, bool>> addStudentToParent(int parentId, String studentId);

  /// Remove student from parent
  Future<Either<Failure, bool>> removeStudentFromParent(int parentId, String studentId);

  /// Search parents by name
  Future<Either<Failure, List<Parent>>> searchParents(String query, {
    int page = 1,
    int limit = 10,
  });
}
