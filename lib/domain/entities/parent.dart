import 'package:equatable/equatable.dart';

/// Parent entity
/// Following Single Responsibility Principle by focusing only on parent data
class Parent extends Equatable {
  final int? id;
  final String? name;
  final String? email;
  final String? phone;
  final String? emailVerifiedAt;
  final String? address;
  final int? status;
  final String? logo;
  final String? typeAuth;
  final String? firebaseToken;
  final String? createdAt;
  final String? updatedAt;
  final String? logoPath;
  final bool? subscriptionStatus;
  final List<String>? childrenIds; // List of children IDs
  final int? childrenCount; // Number of children

  const Parent({
    this.id,
    this.name,
    this.email,
    this.phone,
    this.emailVerifiedAt,
    this.address,
    this.status,
    this.logo,
    this.typeAuth,
    this.firebaseToken,
    this.createdAt,
    this.updatedAt,
    this.logoPath,
    this.subscriptionStatus,
    this.childrenIds,
    this.childrenCount,
  });

  /// Create a copy of this Parent with the given fields replaced with the new values
  Parent copyWith({
    int? id,
    String? name,
    String? email,
    String? phone,
    String? emailVerifiedAt,
    String? address,
    int? status,
    String? logo,
    String? typeAuth,
    String? firebaseToken,
    String? createdAt,
    String? updatedAt,
    String? logoPath,
    bool? subscriptionStatus,
    List<String>? childrenIds,
    int? childrenCount,
  }) {
    return Parent(
      id: id ?? this.id,
      name: name ?? this.name,
      email: email ?? this.email,
      phone: phone ?? this.phone,
      emailVerifiedAt: emailVerifiedAt ?? this.emailVerifiedAt,
      address: address ?? this.address,
      status: status ?? this.status,
      logo: logo ?? this.logo,
      typeAuth: typeAuth ?? this.typeAuth,
      firebaseToken: firebaseToken ?? this.firebaseToken,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      logoPath: logoPath ?? this.logoPath,
      subscriptionStatus: subscriptionStatus ?? this.subscriptionStatus,
      childrenIds: childrenIds ?? this.childrenIds,
      childrenCount: childrenCount ?? this.childrenCount,
    );
  }

  @override
  List<Object?> get props => [
    id,
    name,
    email,
    phone,
    emailVerifiedAt,
    address,
    status,
    logo,
    typeAuth,
    firebaseToken,
    createdAt,
    updatedAt,
    logoPath,
    subscriptionStatus,
    childrenIds,
    childrenCount,
  ];
}
