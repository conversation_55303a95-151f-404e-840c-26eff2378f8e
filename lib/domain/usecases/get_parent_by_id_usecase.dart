import 'package:dartz/dartz.dart';
import '../entities/parent.dart';
import '../repositories/parent_repository.dart';
import '../../core/errors/failures.dart';
import '../../core/usecases/usecase.dart';

/// Use case for getting parent by ID
/// Following Single Responsibility Principle by focusing only on getting parent by ID
class GetParentByIdUseCase implements UseCase<Parent, int> {
  final ParentRepository repository;

  GetParentByIdUseCase(this.repository);

  @override
  Future<Either<Failure, Parent>> call(int id) {
    return repository.getParentById(id);
  }
}
