import 'package:dartz/dartz.dart';
import '../entities/parent.dart';
import '../repositories/parent_repository.dart';
import '../../core/errors/failures.dart';
import '../../core/usecases/usecase.dart';

/// Use case for updating parent
/// Following Single Responsibility Principle by focusing only on updating parent
class UpdateParentUseCase implements UseCase<Parent, Parent> {
  final ParentRepository repository;

  UpdateParentUseCase(this.repository);

  @override
  Future<Either<Failure, Parent>> call(Parent parent) {
    return repository.updateParent(parent);
  }
}
