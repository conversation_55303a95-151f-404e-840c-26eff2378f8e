import 'package:dartz/dartz.dart';
import '../repositories/parent_repository.dart';
import '../../core/errors/failures.dart';
import '../../core/usecases/usecase.dart';

/// Use case for deleting parent
/// Following Single Responsibility Principle by focusing only on deleting parent
class DeleteParentUseCase implements UseCase<bool, int> {
  final ParentRepository repository;

  DeleteParentUseCase(this.repository);

  @override
  Future<Either<Failure, bool>> call(int id) {
    return repository.deleteParent(id);
  }
}
