import 'package:dartz/dartz.dart';
import '../entities/parent.dart';
import '../repositories/parent_repository.dart';
import '../../core/errors/failures.dart';
import '../../core/usecases/usecase.dart';

/// Use case for getting parents
/// Following Single Responsibility Principle by focusing only on getting parents
class GetParentsUseCase implements UseCase<List<Parent>, GetParentsParams> {
  final ParentRepository repository;

  GetParentsUseCase(this.repository);

  @override
  Future<Either<Failure, List<Parent>>> call(GetParentsParams params) {
    return repository.getParents(
      page: params.page,
      limit: params.limit,
      search: params.search,
    );
  }
}

/// Parameters for GetParentsUseCase
class GetParentsParams {
  final int page;
  final int limit;
  final String? search;

  GetParentsParams({
    required this.page,
    required this.limit,
    this.search,
  });
}
