import 'package:dartz/dartz.dart';
import '../entities/parent.dart';
import '../repositories/parent_repository.dart';
import '../../core/errors/failures.dart';
import '../../core/usecases/usecase.dart';

/// Use case for creating parent
/// Following Single Responsibility Principle by focusing only on creating parent
class CreateParentUseCase implements UseCase<Parent, Parent> {
  final ParentRepository repository;

  CreateParentUseCase(this.repository);

  @override
  Future<Either<Failure, Parent>> call(Parent parent) {
    return repository.createParent(parent);
  }
}
