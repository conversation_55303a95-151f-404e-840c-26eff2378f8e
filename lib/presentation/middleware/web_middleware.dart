import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'dart:html' as html;

/// WebMiddleware class for handling web-specific functionality
/// Following Single Responsibility Principle by focusing only on web middleware
class WebMiddleware extends GetMiddleware {
  @override
  int? get priority => 2;

  @override
  RouteSettings? redirect(String? route) {
    // Only apply web-specific logic on web platform
    if (kIsWeb) {
      _updateBrowserTitle(route);
      _updateMetaTags(route);
      _handleWebNavigation(route);
    }
    
    return null;
  }

  @override
  GetPage? onPageCalled(GetPage? page) {
    if (kIsWeb) {
      // Update browser URL and title
      _updateBrowserState(page?.name);
      
      // Handle web-specific page setup
      _setupWebPage(page);
    }
    
    return super.onPageCalled(page);
  }

  @override
  Widget onPageBuilt(Widget page) {
    if (kIsWeb) {
      // Wrap page with web-specific functionality
      return _wrapWithWebFeatures(page);
    }
    
    return super.onPageBuilt(page);
  }

  /// Update browser title based on route
  void _updateBrowserTitle(String? route) {
    final Map<String, String> routeTitles = {
      '/login': 'تسجيل الدخول - باصاتي',
      '/register': 'إنشاء حساب - باصاتي',
      '/forgot-password': 'استعادة كلمة المرور - باصاتي',
      '/home-page': 'الصفحة الرئيسية - باصاتي',
      '/home': 'لوحة التحكم - باصاتي',
      '/students': 'إدارة الطلاب - باصاتي',
      '/students/add': 'إضافة طالب - باصاتي',
      '/students/edit': 'تعديل طالب - باصاتي',
      '/parents': 'إدارة الأولياء - باصاتي',
      '/parents/add': 'إضافة ولي أمر - باصاتي',
      '/parents/edit': 'تعديل ولي أمر - باصاتي',
      '/parents/details': 'تفاصيل ولي الأمر - باصاتي',
      '/buses': 'إدارة الباصات - باصاتي',
      '/buses/add': 'إضافة باص - باصاتي',
      '/buses/edit': 'تعديل باص - باصاتي',
      '/drivers': 'إدارة السائقين - باصاتي',
      '/drivers/add': 'إضافة سائق - باصاتي',
      '/drivers/edit': 'تعديل سائق - باصاتي',
      '/drivers/details': 'تفاصيل السائق - باصاتي',
      '/supervisors': 'إدارة المشرفين - باصاتي',
      '/supervisors/add': 'إضافة مشرف - باصاتي',
      '/supervisors/edit': 'تعديل مشرف - باصاتي',
      '/supervisors/details': 'تفاصيل المشرف - باصاتي',
      '/current-trips': 'الرحلات الحالية - باصاتي',
      '/previous-trips': 'الرحلات السابقة - باصاتي',
      '/previous-trips/details': 'تفاصيل الرحلة - باصاتي',
      '/trips/create': 'إنشاء رحلة - باصاتي',
      '/profile': 'الملف الشخصي - باصاتي',
      '/profile/edit': 'تعديل الملف الشخصي - باصاتي',
      '/change-password': 'تغيير كلمة المرور - باصاتي',
      '/settings': 'الإعدادات - باصاتي',
      '/language-selection': 'اختيار اللغة - باصاتي',
    };

    final title = routeTitles[route] ?? 'باصاتي - إدارة النقل المدرسي';
    html.document.title = title;
  }

  /// Update meta tags for SEO
  void _updateMetaTags(String? route) {
    final Map<String, Map<String, String>> routeMetaTags = {
      '/login': {
        'description': 'تسجيل الدخول إلى نظام باصاتي لإدارة النقل المدرسي',
        'keywords': 'تسجيل دخول, باصاتي, نقل مدرسي, إدارة',
      },
      '/home-page': {
        'description': 'الصفحة الرئيسية لنظام باصاتي لإدارة النقل المدرسي',
        'keywords': 'باصاتي, نقل مدرسي, إدارة, طلاب, باصات',
      },
      '/students': {
        'description': 'إدارة الطلاب في نظام باصاتي للنقل المدرسي',
        'keywords': 'طلاب, إدارة طلاب, نقل مدرسي, باصاتي',
      },
      '/buses': {
        'description': 'إدارة الباصات في نظام باصاتي للنقل المدرسي',
        'keywords': 'باصات, إدارة باصات, نقل مدرسي, باصاتي',
      },
    };

    final metaTags = routeMetaTags[route];
    if (metaTags != null) {
      _updateMetaTag('description', metaTags['description'] ?? '');
      _updateMetaTag('keywords', metaTags['keywords'] ?? '');
    }
  }

  /// Update specific meta tag
  void _updateMetaTag(String name, String content) {
    final metaElement = html.document.querySelector('meta[name="$name"]');
    if (metaElement != null) {
      metaElement.setAttribute('content', content);
    } else {
      final newMeta = html.MetaElement()
        ..name = name
        ..content = content;
      html.document.head?.append(newMeta);
    }
  }

  /// Handle web-specific navigation
  void _handleWebNavigation(String? route) {
    // Add web-specific navigation handling here
    // For example, handling browser back/forward buttons
    debugPrint('🌐 Web navigation to: $route');
  }

  /// Update browser state
  void _updateBrowserState(String? route) {
    if (route != null) {
      // Update browser URL without triggering navigation
      html.window.history.replaceState(null, '', route);
    }
  }

  /// Setup web-specific page configuration
  void _setupWebPage(GetPage? page) {
    // Add any web-specific page setup here
    debugPrint('🔧 Setting up web page: ${page?.name}');
  }

  /// Wrap page with web-specific features
  Widget _wrapWithWebFeatures(Widget page) {
    return Focus(
      autofocus: true,
      child: Shortcuts(
        shortcuts: <LogicalKeySet, Intent>{
          LogicalKeySet(LogicalKeyboardKey.escape): const _EscapeIntent(),
          LogicalKeySet(LogicalKeyboardKey.f5): const _RefreshIntent(),
          LogicalKeySet(LogicalKeyboardKey.control, LogicalKeyboardKey.keyR): const _RefreshIntent(),
        },
        child: Actions(
          actions: <Type, Action<Intent>>{
            _EscapeIntent: _EscapeAction(),
            _RefreshIntent: _RefreshAction(),
          },
          child: page,
        ),
      ),
    );
  }
}

/// Custom intents for keyboard shortcuts
class _EscapeIntent extends Intent {
  const _EscapeIntent();
}

class _RefreshIntent extends Intent {
  const _RefreshIntent();
}

/// Custom actions for keyboard shortcuts
class _EscapeAction extends Action<_EscapeIntent> {
  @override
  Object? invoke(_EscapeIntent intent) {
    // Handle escape key - go back or close dialogs
    if (Get.isDialogOpen ?? false) {
      Get.back();
    } else if (Get.isBottomSheetOpen ?? false) {
      Get.back();
    } else if (Get.isSnackbarOpen) {
      Get.closeAllSnackbars();
    } else {
      // Go back in navigation
      if (Navigator.canPop(Get.context!)) {
        Get.back();
      }
    }
    return null;
  }
}

class _RefreshAction extends Action<_RefreshIntent> {
  @override
  Object? invoke(_RefreshIntent intent) {
    // Handle refresh - reload current page data
    html.window.location.reload();
    return null;
  }
}
