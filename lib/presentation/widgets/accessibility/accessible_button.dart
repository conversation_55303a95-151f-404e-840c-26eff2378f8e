import 'package:flutter/material.dart';
import '../../../core/accessibility/accessibility_helper.dart';
import '../../../core/constants/color_constants.dart';

/// AccessibleButton - Enhanced button with accessibility features
/// Following Single Responsibility Principle by focusing only on accessible button functionality
class AccessibleButton extends StatefulWidget {
  final String text;
  final VoidCallback? onPressed;
  final IconData? icon;
  final bool isOutlined;
  final bool isLoading;
  final double? width;
  final double? height;
  final Color? backgroundColor;
  final Color? textColor;
  final String? semanticLabel;
  final String? semanticHint;
  final bool autofocus;
  final FocusNode? focusNode;

  const AccessibleButton({
    super.key,
    required this.text,
    this.onPressed,
    this.icon,
    this.isOutlined = false,
    this.isLoading = false,
    this.width,
    this.height,
    this.backgroundColor,
    this.textColor,
    this.semanticLabel,
    this.semanticHint,
    this.autofocus = false,
    this.focusNode,
  });

  @override
  State<AccessibleButton> createState() => _AccessibleButtonState();
}

class _AccessibleButtonState extends State<AccessibleButton> {
  late FocusNode _focusNode;
  bool _isHovered = false;
  bool _isFocused = false;

  @override
  void initState() {
    super.initState();
    _focusNode = widget.focusNode ?? FocusNode();
    _focusNode.addListener(_onFocusChange);
  }

  @override
  void dispose() {
    _focusNode.removeListener(_onFocusChange);
    if (widget.focusNode == null) {
      _focusNode.dispose();
    }
    super.dispose();
  }

  void _onFocusChange() {
    setState(() {
      _isFocused = _focusNode.hasFocus;
    });
  }

  @override
  Widget build(BuildContext context) {
    final isEnabled = widget.onPressed != null && !widget.isLoading;
    final semanticLabel = widget.semanticLabel ?? 
        AccessibilityHelper.createButtonLabel(widget.text, hint: widget.semanticHint);

    return Semantics(
      label: semanticLabel,
      button: true,
      enabled: isEnabled,
      focusable: true,
      child: Focus(
        focusNode: _focusNode,
        autofocus: widget.autofocus,
        child: MouseRegion(
          onEnter: (_) => setState(() => _isHovered = true),
          onExit: (_) => setState(() => _isHovered = false),
          child: GestureDetector(
            onTap: isEnabled ? widget.onPressed : null,
            child: AnimatedContainer(
              duration: const Duration(milliseconds: 200),
              width: widget.width,
              height: widget.height ?? 48,
              decoration: BoxDecoration(
                color: _getBackgroundColor(context, isEnabled),
                border: widget.isOutlined ? Border.all(
                  color: _getBorderColor(context, isEnabled),
                  width: _isFocused ? 2 : 1,
                ) : null,
                borderRadius: BorderRadius.circular(8),
                boxShadow: _isFocused ? [
                  BoxShadow(
                    color: ColorConstants.primary.withOpacity(0.3),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ] : null,
              ),
              child: Material(
                color: Colors.transparent,
                child: InkWell(
                  onTap: isEnabled ? widget.onPressed : null,
                  borderRadius: BorderRadius.circular(8),
                  child: Container(
                    padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        if (widget.isLoading) ...[
                          SizedBox(
                            width: 16,
                            height: 16,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              valueColor: AlwaysStoppedAnimation<Color>(
                                _getTextColor(context, isEnabled),
                              ),
                            ),
                          ),
                          const SizedBox(width: 8),
                        ] else if (widget.icon != null) ...[
                          Icon(
                            widget.icon,
                            size: 18,
                            color: _getTextColor(context, isEnabled),
                          ),
                          const SizedBox(width: 8),
                        ],
                        Flexible(
                          child: Text(
                            widget.text,
                            style: TextStyle(
                              color: _getTextColor(context, isEnabled),
                              fontWeight: FontWeight.w600,
                              fontSize: 16,
                            ),
                            textAlign: TextAlign.center,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Color _getBackgroundColor(BuildContext context, bool isEnabled) {
    if (widget.isOutlined) {
      if (_isHovered && isEnabled) {
        return ColorConstants.primary.withOpacity(0.1);
      }
      return Colors.transparent;
    }

    if (!isEnabled) {
      return ColorConstants.disabled;
    }

    if (_isHovered) {
      return (widget.backgroundColor ?? ColorConstants.primary).withOpacity(0.9);
    }

    return widget.backgroundColor ?? ColorConstants.primary;
  }

  Color _getBorderColor(BuildContext context, bool isEnabled) {
    if (!isEnabled) {
      return ColorConstants.disabled;
    }

    if (_isFocused) {
      return ColorConstants.primary;
    }

    return widget.backgroundColor ?? ColorConstants.primary;
  }

  Color _getTextColor(BuildContext context, bool isEnabled) {
    if (!isEnabled) {
      return ColorConstants.textDisabled;
    }

    if (widget.isOutlined) {
      return widget.textColor ?? ColorConstants.primary;
    }

    return widget.textColor ?? ColorConstants.white;
  }
}

/// AccessibleIconButton - Enhanced icon button with accessibility features
class AccessibleIconButton extends StatefulWidget {
  final IconData icon;
  final VoidCallback? onPressed;
  final String semanticLabel;
  final String? semanticHint;
  final Color? color;
  final double size;
  final bool autofocus;
  final FocusNode? focusNode;

  const AccessibleIconButton({
    super.key,
    required this.icon,
    required this.semanticLabel,
    this.onPressed,
    this.semanticHint,
    this.color,
    this.size = 24,
    this.autofocus = false,
    this.focusNode,
  });

  @override
  State<AccessibleIconButton> createState() => _AccessibleIconButtonState();
}

class _AccessibleIconButtonState extends State<AccessibleIconButton> {
  late FocusNode _focusNode;
  bool _isHovered = false;
  bool _isFocused = false;

  @override
  void initState() {
    super.initState();
    _focusNode = widget.focusNode ?? FocusNode();
    _focusNode.addListener(_onFocusChange);
  }

  @override
  void dispose() {
    _focusNode.removeListener(_onFocusChange);
    if (widget.focusNode == null) {
      _focusNode.dispose();
    }
    super.dispose();
  }

  void _onFocusChange() {
    setState(() {
      _isFocused = _focusNode.hasFocus;
    });
  }

  @override
  Widget build(BuildContext context) {
    final isEnabled = widget.onPressed != null;
    final semanticLabel = AccessibilityHelper.createButtonLabel(
      widget.semanticLabel, 
      hint: widget.semanticHint,
    );

    return Semantics(
      label: semanticLabel,
      button: true,
      enabled: isEnabled,
      focusable: true,
      child: Focus(
        focusNode: _focusNode,
        autofocus: widget.autofocus,
        child: MouseRegion(
          onEnter: (_) => setState(() => _isHovered = true),
          onExit: (_) => setState(() => _isHovered = false),
          child: GestureDetector(
            onTap: isEnabled ? widget.onPressed : null,
            child: AnimatedContainer(
              duration: const Duration(milliseconds: 200),
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: _isHovered && isEnabled 
                    ? ColorConstants.primary.withOpacity(0.1) 
                    : Colors.transparent,
                borderRadius: BorderRadius.circular(8),
                border: _isFocused ? Border.all(
                  color: ColorConstants.primary,
                  width: 2,
                ) : null,
              ),
              child: Icon(
                widget.icon,
                size: widget.size,
                color: isEnabled 
                    ? (widget.color ?? ColorConstants.primary)
                    : ColorConstants.disabled,
              ),
            ),
          ),
        ),
      ),
    );
  }
}
