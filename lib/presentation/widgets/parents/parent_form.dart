import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../domain/entities/parent.dart';
import '../../../core/constants/color_constants.dart';
import '../../../core/utils/responsive_utils.dart';
import '../common/custom_text_field.dart';
import '../common/loading_widget.dart';

/// Parent form widget for creating and editing parents
/// Following Single Responsibility Principle by focusing only on parent form UI
class ParentForm extends StatefulWidget {
  final Parent? parent;
  final Function(Parent) onSubmit;
  final bool isLoading;

  const ParentForm({
    super.key,
    this.parent,
    required this.onSubmit,
    this.isLoading = false,
  });

  @override
  State<ParentForm> createState() => _ParentFormState();
}

class _ParentFormState extends State<ParentForm> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _emailController = TextEditingController();
  final _phoneController = TextEditingController();
  final _addressController = TextEditingController();

  int _status = 1; // Active by default

  @override
  void initState() {
    super.initState();
    _initializeForm();
  }

  void _initializeForm() {
    if (widget.parent != null) {
      _nameController.text = widget.parent!.name ?? '';
      _emailController.text = widget.parent!.email ?? '';
      _phoneController.text = widget.parent!.phone ?? '';
      _addressController.text = widget.parent!.address ?? '';
      _status = widget.parent!.status ?? 1;
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _emailController.dispose();
    _phoneController.dispose();
    _addressController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final deviceType = ResponsiveUtils.getDeviceType(context);
    final isMobile = deviceType == DeviceScreenType.mobile;

    return SingleChildScrollView(
      padding: const EdgeInsets.all(24),
      child: Form(
        key: _formKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildFormHeader(),
            const SizedBox(height: 32),
            _buildFormCard(isMobile),
            const SizedBox(height: 32),
            _buildSubmitButton(),
          ],
        ),
      ),
    );
  }

  Widget _buildFormHeader() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            ColorConstants.primary,
            ColorConstants.primary.withValues(alpha: 0.8),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: ColorConstants.primary.withValues(alpha: 0.3),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        children: [
          Icon(Icons.family_restroom, size: 48, color: ColorConstants.white),
          const SizedBox(height: 16),
          Text(
            widget.parent == null ? 'إضافة ولي أمر جديد' : 'تعديل ولي الأمر',
            style: const TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: ColorConstants.white,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 8),
          Text(
            widget.parent == null
                ? 'أدخل بيانات ولي الأمر الجديد'
                : 'قم بتعديل بيانات ولي الأمر',
            style: TextStyle(
              fontSize: 16,
              color: ColorConstants.white.withValues(alpha: 0.9),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildFormCard(bool isMobile) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color:
            Theme.of(context).brightness == Brightness.dark
                ? ColorConstants.surfaceDark
                : ColorConstants.surface,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'المعلومات الأساسية',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color:
                  Theme.of(context).brightness == Brightness.dark
                      ? ColorConstants.textPrimaryDark
                      : ColorConstants.textPrimary,
            ),
          ),
          const SizedBox(height: 24),
          if (isMobile) ...[
            _buildNameField(),
            const SizedBox(height: 16),
            _buildEmailField(),
            const SizedBox(height: 16),
            _buildPhoneField(),
            const SizedBox(height: 16),
            _buildAddressField(),
            const SizedBox(height: 16),
            _buildStatusField(),
          ] else ...[
            Row(
              children: [
                Expanded(child: _buildNameField()),
                const SizedBox(width: 16),
                Expanded(child: _buildEmailField()),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(child: _buildPhoneField()),
                const SizedBox(width: 16),
                Expanded(child: _buildStatusField()),
              ],
            ),
            const SizedBox(height: 16),
            _buildAddressField(),
          ],
        ],
      ),
    );
  }

  Widget _buildNameField() {
    return CustomTextField(
      controller: _nameController,
      labelText: 'اسم ولي الأمر',
      hintText: 'أدخل اسم ولي الأمر',
      prefixIcon: Icons.person,
      validator: (value) {
        if (value == null || value.trim().isEmpty) {
          return 'اسم ولي الأمر مطلوب';
        }
        if (value.trim().length < 2) {
          return 'اسم ولي الأمر يجب أن يكون أكثر من حرفين';
        }
        return null;
      },
    );
  }

  Widget _buildEmailField() {
    return CustomTextField(
      controller: _emailController,
      labelText: 'البريد الإلكتروني',
      hintText: 'أدخل البريد الإلكتروني',
      prefixIcon: Icons.email,
      keyboardType: TextInputType.emailAddress,
      validator: (value) {
        if (value == null || value.trim().isEmpty) {
          return 'البريد الإلكتروني مطلوب';
        }
        if (!GetUtils.isEmail(value.trim())) {
          return 'البريد الإلكتروني غير صحيح';
        }
        return null;
      },
    );
  }

  Widget _buildPhoneField() {
    return CustomTextField(
      controller: _phoneController,
      labelText: 'رقم الهاتف',
      hintText: 'أدخل رقم الهاتف',
      prefixIcon: Icons.phone,
      keyboardType: TextInputType.phone,
      validator: (value) {
        if (value == null || value.trim().isEmpty) {
          return 'رقم الهاتف مطلوب';
        }
        if (value.trim().length < 10) {
          return 'رقم الهاتف يجب أن يكون 10 أرقام على الأقل';
        }
        return null;
      },
    );
  }

  Widget _buildAddressField() {
    return CustomTextField(
      controller: _addressController,
      labelText: 'العنوان',
      hintText: 'أدخل العنوان',
      prefixIcon: Icons.location_on,
      maxLines: 3,
      validator: (value) {
        if (value == null || value.trim().isEmpty) {
          return 'العنوان مطلوب';
        }
        return null;
      },
    );
  }

  Widget _buildStatusField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'الحالة',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w500,
            color:
                Theme.of(context).brightness == Brightness.dark
                    ? ColorConstants.textPrimaryDark
                    : ColorConstants.textPrimary,
          ),
        ),
        const SizedBox(height: 8),
        Container(
          width: double.infinity,
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
          decoration: BoxDecoration(
            border: Border.all(
              color:
                  Theme.of(context).brightness == Brightness.dark
                      ? ColorConstants.textSecondaryDark
                      : ColorConstants.textSecondary,
            ),
            borderRadius: BorderRadius.circular(8),
          ),
          child: DropdownButtonHideUnderline(
            child: DropdownButton<int>(
              value: _status,
              isExpanded: true,
              items: const [
                DropdownMenuItem(value: 1, child: Text('نشط')),
                DropdownMenuItem(value: 0, child: Text('غير نشط')),
              ],
              onChanged: (value) {
                if (value != null) {
                  setState(() {
                    _status = value;
                  });
                }
              },
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildSubmitButton() {
    return SizedBox(
      width: double.infinity,
      height: 56,
      child: ElevatedButton(
        onPressed: widget.isLoading ? null : _handleSubmit,
        style: ElevatedButton.styleFrom(
          backgroundColor: ColorConstants.primary,
          foregroundColor: ColorConstants.white,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          elevation: 2,
        ),
        child:
            widget.isLoading
                ? const LoadingWidget(size: 24)
                : Text(
                  widget.parent == null ? 'إضافة ولي الأمر' : 'تحديث ولي الأمر',
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                  ),
                ),
      ),
    );
  }

  void _handleSubmit() {
    if (_formKey.currentState?.validate() ?? false) {
      final parent = Parent(
        id: widget.parent?.id,
        name: _nameController.text.trim(),
        email: _emailController.text.trim(),
        phone: _phoneController.text.trim(),
        address: _addressController.text.trim(),
        status: _status,
        createdAt: widget.parent?.createdAt,
        updatedAt: widget.parent?.updatedAt,
      );

      widget.onSubmit(parent);
    }
  }
}
