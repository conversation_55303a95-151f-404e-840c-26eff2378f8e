import 'package:get/get.dart';
import '../../domain/entities/parent.dart';
import '../../domain/entities/student.dart';
import '../../domain/usecases/get_parents_usecase.dart';
import '../../domain/usecases/get_parent_by_id_usecase.dart';
import '../../domain/usecases/create_parent_usecase.dart';
import '../../domain/usecases/update_parent_usecase.dart';
import '../../domain/usecases/delete_parent_usecase.dart';
import '../../domain/usecases/get_students_by_parent_id_usecase.dart';
import '../../core/utils/logger.dart';

/// Parents controller using GetX state management
/// Following Single Responsibility Principle by focusing only on parents state management
class ParentsController extends GetxController {
  final GetParentsUseCase _getParentsUseCase;
  final GetParentByIdUseCase _getParentByIdUseCase;
  final CreateParentUseCase _createParentUseCase;
  final UpdateParentUseCase _updateParentUseCase;
  final DeleteParentUseCase _deleteParentUseCase;
  final GetStudentsByParentIdUseCase _getStudentsByParentIdUseCase;

  ParentsController({
    required GetParentsUseCase getParentsUseCase,
    required GetParentByIdUseCase getParentByIdUseCase,
    required CreateParentUseCase createParentUseCase,
    required UpdateParentUseCase updateParentUseCase,
    required DeleteParentUseCase deleteParentUseCase,
    required GetStudentsByParentIdUseCase getStudentsByParentIdUseCase,
  })  : _getParentsUseCase = getParentsUseCase,
        _getParentByIdUseCase = getParentByIdUseCase,
        _createParentUseCase = createParentUseCase,
        _updateParentUseCase = updateParentUseCase,
        _deleteParentUseCase = deleteParentUseCase,
        _getStudentsByParentIdUseCase = getStudentsByParentIdUseCase;

  // Observable state
  final RxList<Parent> _parents = <Parent>[].obs;
  final RxBool _isLoading = false.obs;
  final RxBool _isLoadingMore = false.obs;
  final RxString _errorMessage = ''.obs;
  final RxString _searchQuery = ''.obs;
  final RxInt _currentPage = 1.obs;
  final RxBool _hasMoreData = true.obs;

  // Getters
  List<Parent> get parents => _parents;
  bool get isLoading => _isLoading.value;
  bool get isLoadingMore => _isLoadingMore.value;
  String get errorMessage => _errorMessage.value;
  String get searchQuery => _searchQuery.value;
  int get currentPage => _currentPage.value;
  bool get hasMoreData => _hasMoreData.value;

  @override
  void onInit() {
    super.onInit();
    loadParents();
  }

  /// Load parents with pagination
  Future<void> loadParents({bool refresh = false}) async {
    try {
      if (refresh) {
        _currentPage.value = 1;
        _hasMoreData.value = true;
        _parents.clear();
      }

      if (!_hasMoreData.value && !refresh) return;

      if (_currentPage.value == 1) {
        _isLoading.value = true;
      } else {
        _isLoadingMore.value = true;
      }

      _errorMessage.value = '';

      LoggerService.debug('Loading parents', data: {
        'page': _currentPage.value,
        'refresh': refresh,
        'search': _searchQuery.value,
      });

      final result = await _getParentsUseCase(GetParentsParams(
        page: _currentPage.value,
        limit: 10,
        search: _searchQuery.value.isEmpty ? null : _searchQuery.value,
      ));

      result.fold(
        (failure) {
          LoggerService.error('Failed to load parents', error: failure);
          _errorMessage.value = failure.message;
          Get.snackbar(
            'خطأ',
            failure.message,
            snackPosition: SnackPosition.BOTTOM,
          );
        },
        (newParents) {
          LoggerService.info('Successfully loaded ${newParents.length} parents');
          
          if (refresh) {
            _parents.assignAll(newParents);
          } else {
            _parents.addAll(newParents);
          }

          // Check if there's more data
          if (newParents.length < 10) {
            _hasMoreData.value = false;
          } else {
            _currentPage.value++;
          }
        },
      );
    } catch (e) {
      LoggerService.error('Unexpected error loading parents', error: e);
      _errorMessage.value = 'حدث خطأ غير متوقع';
      Get.snackbar(
        'خطأ',
        'حدث خطأ غير متوقع',
        snackPosition: SnackPosition.BOTTOM,
      );
    } finally {
      _isLoading.value = false;
      _isLoadingMore.value = false;
    }
  }

  /// Load more parents for pagination
  Future<void> loadMoreParents() async {
    if (!_isLoadingMore.value && _hasMoreData.value) {
      await loadParents();
    }
  }

  /// Search parents
  void searchParents(String query) {
    _searchQuery.value = query;
    loadParents(refresh: true);
  }

  /// Get parent by ID
  Future<Parent?> getParentById(int id) async {
    try {
      LoggerService.debug('Getting parent by ID', data: {'parentId': id});

      final result = await _getParentByIdUseCase(id);

      return result.fold(
        (failure) {
          LoggerService.error('Failed to get parent', error: failure);
          Get.snackbar(
            'خطأ',
            failure.message,
            snackPosition: SnackPosition.BOTTOM,
          );
          return null;
        },
        (parent) {
          LoggerService.info('Successfully retrieved parent', data: {'parentId': id});
          return parent;
        },
      );
    } catch (e) {
      LoggerService.error('Unexpected error getting parent', error: e);
      Get.snackbar(
        'خطأ',
        'حدث خطأ غير متوقع',
        snackPosition: SnackPosition.BOTTOM,
      );
      return null;
    }
  }

  /// Create parent
  Future<bool> createParent(Parent parent) async {
    try {
      LoggerService.debug('Creating parent', data: {'parentName': parent.name});

      _isLoading.value = true;
      _errorMessage.value = '';

      final result = await _createParentUseCase(parent);

      return result.fold(
        (failure) {
          LoggerService.error('Failed to create parent', error: failure);
          _errorMessage.value = failure.message;
          Get.snackbar(
            'خطأ',
            failure.message,
            snackPosition: SnackPosition.BOTTOM,
          );
          return false;
        },
        (createdParent) {
          LoggerService.info('Successfully created parent', data: {'parentId': createdParent.id});
          
          // Add to the beginning of the list
          _parents.insert(0, createdParent);
          
          Get.snackbar(
            'نجح',
            'تم إنشاء ولي الأمر بنجاح',
            snackPosition: SnackPosition.BOTTOM,
          );
          return true;
        },
      );
    } catch (e) {
      LoggerService.error('Unexpected error creating parent', error: e);
      _errorMessage.value = 'حدث خطأ غير متوقع';
      Get.snackbar(
        'خطأ',
        'حدث خطأ غير متوقع',
        snackPosition: SnackPosition.BOTTOM,
      );
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  /// Update parent
  Future<bool> updateParent(Parent parent) async {
    try {
      LoggerService.debug('Updating parent', data: {'parentId': parent.id});

      _isLoading.value = true;
      _errorMessage.value = '';

      final result = await _updateParentUseCase(parent);

      return result.fold(
        (failure) {
          LoggerService.error('Failed to update parent', error: failure);
          _errorMessage.value = failure.message;
          Get.snackbar(
            'خطأ',
            failure.message,
            snackPosition: SnackPosition.BOTTOM,
          );
          return false;
        },
        (updatedParent) {
          LoggerService.info('Successfully updated parent', data: {'parentId': parent.id});

          // Update in the list
          final index = _parents.indexWhere((p) => p.id == parent.id);
          if (index != -1) {
            _parents[index] = updatedParent;
          }

          Get.snackbar(
            'نجح',
            'تم تحديث ولي الأمر بنجاح',
            snackPosition: SnackPosition.BOTTOM,
          );
          return true;
        },
      );
    } catch (e) {
      LoggerService.error('Unexpected error updating parent', error: e);
      _errorMessage.value = 'حدث خطأ غير متوقع';
      Get.snackbar(
        'خطأ',
        'حدث خطأ غير متوقع',
        snackPosition: SnackPosition.BOTTOM,
      );
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  /// Delete parent
  Future<bool> deleteParent(int parentId) async {
    try {
      LoggerService.debug('Deleting parent', data: {'parentId': parentId});

      _isLoading.value = true;
      _errorMessage.value = '';

      final result = await _deleteParentUseCase(parentId);

      return result.fold(
        (failure) {
          LoggerService.error('Failed to delete parent', error: failure);
          _errorMessage.value = failure.message;
          Get.snackbar(
            'خطأ',
            failure.message,
            snackPosition: SnackPosition.BOTTOM,
          );
          return false;
        },
        (success) {
          LoggerService.info('Successfully deleted parent', data: {'parentId': parentId});

          // Remove from the list
          _parents.removeWhere((p) => p.id == parentId);

          Get.snackbar(
            'نجح',
            'تم حذف ولي الأمر بنجاح',
            snackPosition: SnackPosition.BOTTOM,
          );
          return true;
        },
      );
    } catch (e) {
      LoggerService.error('Unexpected error deleting parent', error: e);
      _errorMessage.value = 'حدث خطأ غير متوقع';
      Get.snackbar(
        'خطأ',
        'حدث خطأ غير متوقع',
        snackPosition: SnackPosition.BOTTOM,
      );
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  /// Get students by parent ID
  Future<List<Student>> getStudentsByParentId(String parentId) async {
    try {
      LoggerService.debug('Getting students by parent ID', data: {'parentId': parentId});

      final result = await _getStudentsByParentIdUseCase(
        GetStudentsByParentIdParams(parentId: parentId),
      );

      return result.fold(
        (failure) {
          LoggerService.error('Failed to get students by parent ID', error: failure);
          Get.snackbar(
            'خطأ',
            failure.message,
            snackPosition: SnackPosition.BOTTOM,
          );
          return [];
        },
        (students) {
          LoggerService.info('Successfully retrieved ${students.length} students for parent');
          return students;
        },
      );
    } catch (e) {
      LoggerService.error('Unexpected error getting students by parent ID', error: e);
      Get.snackbar(
        'خطأ',
        'حدث خطأ غير متوقع',
        snackPosition: SnackPosition.BOTTOM,
      );
      return [];
    }
  }

  /// Refresh all data
  Future<void> refreshData() async {
    await loadParents(refresh: true);
  }

  /// Clear error message
  void clearError() {
    _errorMessage.value = '';
  }
}
