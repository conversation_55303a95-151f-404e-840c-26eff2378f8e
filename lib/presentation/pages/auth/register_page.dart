import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../core/constants/color_constants.dart';
import '../../../core/utils/responsive_utils.dart';
import '../../../core/widgets/responsive_builder.dart';
import '../../../data/models/user_model.dart';
import '../../controllers/auth_controller.dart';
import '../../routes/app_routes.dart';
import '../../widgets/auth/auth_header.dart';
import '../../widgets/auth/auth_text_field.dart';
import '../../widgets/common/custom_button.dart';

/// RegisterPage for user registration
/// Following Single Responsibility Principle by focusing only on registration UI
class RegisterPage extends StatefulWidget {
  const RegisterPage({Key? key}) : super(key: key);

  @override
  State<RegisterPage> createState() => _RegisterPageState();
}

class _RegisterPageState extends State<RegisterPage> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _emailController = TextEditingController();
  final _phoneController = TextEditingController();
  final _passwordController = TextEditingController();
  final _confirmPasswordController = TextEditingController();
  final _addressController = TextEditingController();
  final _authController = Get.find<AuthController>();
  bool _obscurePassword = true;
  bool _obscureConfirmPassword = true;
  double? _latitude;
  double? _longitude;

  @override
  void dispose() {
    _nameController.dispose();
    _emailController.dispose();
    _phoneController.dispose();
    _passwordController.dispose();
    _confirmPasswordController.dispose();
    _addressController.dispose();
    super.dispose();
  }

  /// Open map to select location
  Future<void> _selectLocationOnMap() async {
    final result = await Get.toNamed('/select-location');
    if (result != null && result is Map<String, double>) {
      setState(() {
        _latitude = result['latitude'];
        _longitude = result['longitude'];
      });

      Get.snackbar(
        'تم تحديد الموقع',
        'تم تحديد موقعك على الخريطة بنجاح',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: ColorConstants.success.withAlpha(150),
        colorText: ColorConstants.white,
        margin: const EdgeInsets.all(16),
      );
    }
  }

  /// Validate form and register
  Future<void> _register() async {
    if (_formKey.currentState!.validate()) {
      // Check if location is selected
      if (_latitude == null || _longitude == null) {
        Get.snackbar(
          'موقع مطلوب',
          'الرجاء تحديد موقعك على الخريطة',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: ColorConstants.warning.withAlpha(150),
          colorText: ColorConstants.white,
          margin: const EdgeInsets.all(16),
        );
        return;
      }

      // Create user model with required fields only
      final user = UserModel(
        id: '', // Will be generated by the server
        name: _nameController.text.trim(),
        email: _emailController.text.trim(),
        phone: _phoneController.text.trim(),
        address: _addressController.text.trim(),
        latitude: _latitude,
        longitude: _longitude,
      );

      final success = await _authController.register(
        user,
        _passwordController.text,
      );

      if (success) {
        Get.offAllNamed(AppRoutes.home);
      } else {
        // Show detailed error message to user
        final errorMessage =
            _authController.errorMessage.isNotEmpty
                ? _authController.errorMessage
                : 'حدث خطأ أثناء التسجيل، الرجاء المحاولة مرة أخرى';

        Get.snackbar(
          'خطأ في التسجيل',
          errorMessage,
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: ColorConstants.error.withAlpha(200),
          colorText: ColorConstants.white,
          margin: const EdgeInsets.all(16),
          duration: const Duration(seconds: 5),
          maxWidth: 400,
          borderRadius: 8,
          icon: const Icon(Icons.error_outline, color: Colors.white),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              ColorConstants.primary,
              ColorConstants.primary.withValues(alpha: 0.8),
              ColorConstants.primary.withValues(alpha: 0.9),
            ],
          ),
        ),
        child: ResponsiveBuilder(
          builder: (context, screenType) {
            return SafeArea(
              child: Center(
                child: SingleChildScrollView(
                  padding: const EdgeInsets.all(24),
                  child: Container(
                    width: ResponsiveUtils.getResponsiveValue<double>(
                      context: context,
                      mobile: MediaQuery.of(context).size.width * 0.9,
                      tablet: 600,
                      desktop: 700,
                    ),
                    padding: const EdgeInsets.all(24),
                    decoration: BoxDecoration(
                      color: ColorConstants.white,
                      borderRadius: BorderRadius.circular(16),
                      boxShadow: [
                        BoxShadow(
                          color: ColorConstants.black.withValues(alpha: 0.3),
                          blurRadius: 30,
                          offset: const Offset(0, 15),
                        ),
                        BoxShadow(
                          color: ColorConstants.black.withValues(alpha: 0.1),
                          blurRadius: 10,
                          offset: const Offset(0, 5),
                        ),
                      ],
                    ),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        // Header with logo and title
                        const AuthHeader(
                          title: 'إنشاء حساب جديد',
                          subtitle: 'أدخل بياناتك لإنشاء حساب جديد',
                        ),
                        const SizedBox(height: 32),

                        // Registration form
                        Form(
                          key: _formKey,
                          child: Column(
                            children: [
                              // Name field
                              AuthTextField(
                                controller: _nameController,
                                label: 'الاسم',
                                hint: 'أدخل الاسم الكامل',
                                icon: Icons.person_outline,
                                validator: (value) {
                                  if (value == null || value.isEmpty) {
                                    return 'الرجاء إدخال الاسم';
                                  }
                                  return null;
                                },
                              ),
                              const SizedBox(height: 12),

                              // Email field
                              AuthTextField(
                                controller: _emailController,
                                label: 'البريد الإلكتروني',
                                hint: 'أدخل البريد الإلكتروني',
                                icon: Icons.email_outlined,
                                keyboardType: TextInputType.emailAddress,
                                validator: (value) {
                                  if (value == null || value.isEmpty) {
                                    return 'الرجاء إدخال البريد الإلكتروني';
                                  }
                                  if (!GetUtils.isEmail(value)) {
                                    return 'الرجاء إدخال بريد إلكتروني صحيح';
                                  }
                                  return null;
                                },
                              ),
                              const SizedBox(height: 12),

                              // Phone field
                              AuthTextField(
                                controller: _phoneController,
                                label: 'رقم الهاتف',
                                hint: 'أدخل رقم الهاتف (مثال: 0591234567)',
                                icon: Icons.phone_outlined,
                                keyboardType: TextInputType.phone,
                                validator: (value) {
                                  if (value == null || value.isEmpty) {
                                    return 'الرجاء إدخال رقم الهاتف';
                                  }
                                  if (value.length < 10) {
                                    return 'رقم الهاتف يجب أن يكون على الأقل 10 أرقام';
                                  }
                                  return null;
                                },
                              ),
                              const SizedBox(height: 12),

                              // Address field
                              AuthTextField(
                                controller: _addressController,
                                label: 'العنوان',
                                hint: 'أدخل عنوانك (على الأقل 5 أحرف)',
                                icon: Icons.location_on_outlined,
                                validator: (value) {
                                  if (value == null || value.isEmpty) {
                                    return 'الرجاء إدخال العنوان';
                                  }
                                  if (value.length < 5) {
                                    return 'العنوان يجب أن يحتوي على الأقل 5 أحرف';
                                  }
                                  return null;
                                },
                              ),
                              const SizedBox(height: 12),

                              // Location button
                              SizedBox(
                                width: double.infinity,
                                child: OutlinedButton.icon(
                                  onPressed: _selectLocationOnMap,
                                  style: OutlinedButton.styleFrom(
                                    minimumSize: const Size(
                                      double.infinity,
                                      40,
                                    ),
                                    padding: const EdgeInsets.symmetric(
                                      vertical: 8,
                                      horizontal: 16,
                                    ),
                                    side: BorderSide(
                                      color: ColorConstants.primary,
                                    ),
                                    shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(10),
                                    ),
                                  ),
                                  icon: Icon(
                                    Icons.location_on,
                                    color: ColorConstants.primary,
                                    size: 18,
                                  ),
                                  label: Text(
                                    _latitude != null && _longitude != null
                                        ? 'تم تحديد الموقع ✓'
                                        : 'تحديد الموقع على الخريطة',
                                    style: TextStyle(
                                      color: ColorConstants.primary,
                                      fontWeight: FontWeight.bold,
                                      fontSize: 13,
                                    ),
                                  ),
                                ),
                              ),
                              const SizedBox(height: 12),

                              // Password field
                              AuthTextField(
                                controller: _passwordController,
                                label: 'كلمة المرور',
                                hint: 'أدخل كلمة المرور',
                                icon: Icons.lock_outline,
                                obscureText: _obscurePassword,
                                suffixIcon: IconButton(
                                  icon: Icon(
                                    _obscurePassword
                                        ? Icons.visibility_outlined
                                        : Icons.visibility_off_outlined,
                                    color: ColorConstants.iconInputColor,
                                  ),
                                  onPressed: () {
                                    setState(() {
                                      _obscurePassword = !_obscurePassword;
                                    });
                                  },
                                ),
                                validator: (value) {
                                  if (value == null || value.isEmpty) {
                                    return 'الرجاء إدخال كلمة المرور';
                                  }
                                  if (value.length < 6) {
                                    return 'كلمة المرور يجب أن تكون 6 أحرف على الأقل';
                                  }
                                  return null;
                                },
                              ),
                              const SizedBox(height: 12),

                              // Confirm password field
                              AuthTextField(
                                controller: _confirmPasswordController,
                                label: 'تأكيد كلمة المرور',
                                hint: 'أدخل تأكيد كلمة المرور',
                                icon: Icons.lock_outline,
                                obscureText: _obscureConfirmPassword,
                                suffixIcon: IconButton(
                                  icon: Icon(
                                    _obscureConfirmPassword
                                        ? Icons.visibility_outlined
                                        : Icons.visibility_off_outlined,
                                    color: ColorConstants.iconInputColor,
                                  ),
                                  onPressed: () {
                                    setState(() {
                                      _obscureConfirmPassword =
                                          !_obscureConfirmPassword;
                                    });
                                  },
                                ),
                                validator: (value) {
                                  if (value == null || value.isEmpty) {
                                    return 'الرجاء إدخال تأكيد كلمة المرور';
                                  }
                                  if (value != _passwordController.text) {
                                    return 'كلمة المرور غير متطابقة';
                                  }
                                  return null;
                                },
                              ),
                              const SizedBox(height: 20),

                              // Register button with enhanced styling
                              Obx(() {
                                return CustomButton(
                                  text: 'إنشاء حساب جديد',
                                  isLoading: _authController.isLoading,
                                  onPressed: _register,
                                  height: 44,
                                  borderRadius: 10,
                                  icon: Icons.person_add_rounded,
                                );
                              }),
                              const SizedBox(height: 20),

                              // Login link with enhanced styling
                              Container(
                                padding: const EdgeInsets.symmetric(
                                  vertical: 16,
                                ),
                                child: Row(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    Text(
                                      'لديك حساب بالفعل؟',
                                      style: TextStyle(
                                        color: ColorConstants.textSecondary,
                                        fontSize: 14,
                                        fontWeight: FontWeight.w500,
                                      ),
                                    ),
                                    const SizedBox(width: 4),
                                    TextButton(
                                      onPressed: () {
                                        Get.toNamed(AppRoutes.login);
                                      },
                                      style: TextButton.styleFrom(
                                        padding: const EdgeInsets.symmetric(
                                          horizontal: 12,
                                          vertical: 8,
                                        ),
                                        shape: RoundedRectangleBorder(
                                          borderRadius: BorderRadius.circular(
                                            8,
                                          ),
                                        ),
                                      ),
                                      child: Text(
                                        'تسجيل الدخول',
                                        style: TextStyle(
                                          color: ColorConstants.primary,
                                          fontSize: 14,
                                          fontWeight: FontWeight.bold,
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            );
          },
        ),
      ),
    );
  }
}
