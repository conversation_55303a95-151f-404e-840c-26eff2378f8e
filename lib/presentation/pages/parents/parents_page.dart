import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../controllers/parents_controller.dart';
import '../../widgets/layout/responsive_sidebar.dart';
import '../../widgets/parents/parent_card.dart';
import '../../widgets/common/loading_widget.dart';
import '../../widgets/common/empty_state_widget.dart';
import '../../widgets/common/search_field.dart';
import '../../routes/app_routes.dart';
import '../../../core/constants/color_constants.dart';
import '../../../core/utils/responsive_utils.dart';

/// Parents management page with responsive design
/// Following Single Responsibility Principle by focusing only on parents listing
class ParentsPage extends StatefulWidget {
  const ParentsPage({super.key});

  @override
  State<ParentsPage> createState() => _ParentsPageState();
}

class _ParentsPageState extends State<ParentsPage> {
  final ParentsController _controller = Get.find<ParentsController>();
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    _setupScrollListener();
  }

  void _setupScrollListener() {
    _scrollController.addListener(() {
      if (_scrollController.position.pixels >=
          _scrollController.position.maxScrollExtent - 200) {
        _controller.loadMoreParents();
      }
    });
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return ResponsiveSidebar(
      child: Scaffold(
        backgroundColor: Theme.of(context).brightness == Brightness.dark 
            ? ColorConstants.backgroundDark 
            : ColorConstants.background,
        appBar: _buildAppBar(context),
        body: _buildBody(context),
        floatingActionButton: _buildFloatingActionButton(),
      ),
    );
  }

  PreferredSizeWidget _buildAppBar(BuildContext context) {
    return AppBar(
      title: const Text(
        'إدارة أولياء الأمور',
        style: TextStyle(
          fontWeight: FontWeight.w600,
          color: ColorConstants.white,
        ),
      ),
      backgroundColor: ColorConstants.primary,
      elevation: 0,
      actions: [
        IconButton(
          icon: const Icon(Icons.refresh, color: ColorConstants.white),
          onPressed: () => _controller.refreshData(),
          tooltip: 'تحديث البيانات',
        ),
        IconButton(
          icon: const Icon(Icons.add, color: ColorConstants.white),
          onPressed: () => Get.toNamed(AppRoutes.addParent),
          tooltip: 'إضافة ولي أمر جديد',
        ),
      ],
    );
  }

  Widget _buildBody(BuildContext context) {
    final deviceType = ResponsiveUtils.getDeviceType(context);
    final isMobile = deviceType == DeviceScreenType.mobile;

    return Column(
      children: [
        _buildHeader(isMobile),
        Expanded(
          child: Obx(() => _buildContent()),
        ),
      ],
    );
  }

  Widget _buildHeader(bool isMobile) {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Theme.of(context).brightness == Brightness.dark
            ? ColorConstants.surfaceDark
            : ColorConstants.surface,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (!isMobile) ...[
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                _buildPageTitle(),
                _buildAddButton(),
              ],
            ),
            const SizedBox(height: 24),
          ] else ...[
            _buildPageTitle(),
            const SizedBox(height: 16),
          ],
          _buildSearchField(),
          const SizedBox(height: 16),
          _buildStatsRow(),
        ],
      ),
    );
  }

  Widget _buildPageTitle() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'إدارة أولياء الأمور',
          style: TextStyle(
            fontSize: 28,
            fontWeight: FontWeight.bold,
            color: Theme.of(context).brightness == Brightness.dark
                ? ColorConstants.textPrimaryDark
                : ColorConstants.textPrimary,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          'إدارة وتنظيم بيانات أولياء الأمور',
          style: TextStyle(
            fontSize: 16,
            color: Theme.of(context).brightness == Brightness.dark
                ? ColorConstants.textSecondaryDark
                : ColorConstants.textSecondary,
          ),
        ),
      ],
    );
  }

  Widget _buildAddButton() {
    return ElevatedButton.icon(
      onPressed: () => Get.toNamed(AppRoutes.addParent),
      icon: const Icon(Icons.add, color: ColorConstants.white),
      label: const Text(
        'إضافة ولي أمر',
        style: TextStyle(color: ColorConstants.white),
      ),
      style: ElevatedButton.styleFrom(
        backgroundColor: ColorConstants.primary,
        padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      ),
    );
  }

  Widget _buildSearchField() {
    return SearchField(
      hint: 'البحث عن ولي أمر...',
      onChanged: (query) => _controller.searchParents(query),
    );
  }

  Widget _buildStatsRow() {
    return Obx(() {
      final parentsCount = _controller.parents.length;
      return Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: ColorConstants.primary.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: ColorConstants.primary.withValues(alpha: 0.3),
            width: 1,
          ),
        ),
        child: Row(
          children: [
            Icon(
              Icons.family_restroom,
              color: ColorConstants.primary,
              size: 24,
            ),
            const SizedBox(width: 12),
            Text(
              'إجمالي أولياء الأمور: $parentsCount',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: ColorConstants.primary,
              ),
            ),
          ],
        ),
      );
    });
  }

  Widget _buildContent() {
    if (_controller.isLoading && _controller.parents.isEmpty) {
      return const Center(
        child: LoadingWidget(message: 'جاري تحميل أولياء الأمور...'),
      );
    }

    if (_controller.parents.isEmpty) {
      return EmptyStateWidget(
        icon: Icons.family_restroom,
        title: 'لا توجد أولياء أمور',
        description: _controller.searchQuery.isNotEmpty
            ? 'لم يتم العثور على أولياء أمور مطابقين للبحث'
            : 'لم يتم إضافة أي ولي أمر بعد',
        actionText: 'إضافة ولي أمر جديد',
        onAction: () => Get.toNamed(AppRoutes.addParent),
      );
    }

    return RefreshIndicator(
      onRefresh: () => _controller.refreshData(),
      child: ListView.builder(
        controller: _scrollController,
        padding: const EdgeInsets.all(24),
        itemCount: _controller.parents.length + (_controller.isLoadingMore ? 1 : 0),
        itemBuilder: (context, index) {
          if (index == _controller.parents.length) {
            return const Center(
              child: Padding(
                padding: EdgeInsets.all(16),
                child: LoadingWidget(message: 'جاري تحميل المزيد...'),
              ),
            );
          }

          final parent = _controller.parents[index];
          return ParentCard(
            parent: parent,
            onDelete: () => _showDeleteDialog(parent),
          );
        },
      ),
    );
  }

  Widget _buildFloatingActionButton() {
    return FloatingActionButton(
      onPressed: () => Get.toNamed(AppRoutes.addParent),
      backgroundColor: ColorConstants.primary,
      child: const Icon(Icons.add, color: ColorConstants.white),
    );
  }

  void _showDeleteDialog(parent) {
    Get.dialog(
      AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: Text('هل أنت متأكد من حذف ولي الأمر "${parent.name}"؟'),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () {
              Get.back();
              _controller.deleteParent(parent.id!);
            },
            style: TextButton.styleFrom(
              foregroundColor: ColorConstants.error,
            ),
            child: const Text('حذف'),
          ),
        ],
      ),
    );
  }
}
