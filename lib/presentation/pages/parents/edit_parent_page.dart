import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../controllers/parents_controller.dart';
import '../../widgets/layout/responsive_sidebar.dart';
import '../../widgets/parents/parent_form.dart';
import '../../widgets/common/loading_widget.dart';
import '../../../core/constants/color_constants.dart';
import '../../../domain/entities/parent.dart';

/// Edit parent page with responsive design
/// Following Single Responsibility Principle by focusing only on parent editing
class EditParentPage extends StatefulWidget {
  const EditParentPage({super.key});

  @override
  State<EditParentPage> createState() => _EditParentPageState();
}

class _EditParentPageState extends State<EditParentPage> {
  final ParentsController _controller = Get.find<ParentsController>();
  Parent? _parent;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadParent();
  }

  Future<void> _loadParent() async {
    final parent = Get.arguments as Parent?;
    if (parent != null) {
      setState(() {
        _parent = parent;
        _isLoading = false;
      });
    } else {
      // If no parent passed, try to get from route parameters
      final parentId = Get.parameters['id'];
      if (parentId != null) {
        final fetchedParent = await _controller.getParentById(int.parse(parentId));
        if (fetchedParent != null) {
          setState(() {
            _parent = fetchedParent;
            _isLoading = false;
          });
        } else {
          Get.back();
          Get.snackbar(
            'خطأ',
            'لم يتم العثور على ولي الأمر',
            snackPosition: SnackPosition.BOTTOM,
          );
        }
      } else {
        Get.back();
        Get.snackbar(
          'خطأ',
          'معرف ولي الأمر مطلوب',
          snackPosition: SnackPosition.BOTTOM,
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return ResponsiveSidebar(
      child: Scaffold(
        backgroundColor: Theme.of(context).brightness == Brightness.dark 
            ? ColorConstants.backgroundDark 
            : ColorConstants.background,
        appBar: _buildAppBar(context),
        body: _buildBody(context),
      ),
    );
  }

  PreferredSizeWidget _buildAppBar(BuildContext context) {
    return AppBar(
      title: const Text(
        'تعديل ولي الأمر',
        style: TextStyle(
          fontWeight: FontWeight.w600,
          color: ColorConstants.white,
        ),
      ),
      backgroundColor: ColorConstants.primary,
      elevation: 0,
      leading: IconButton(
        icon: const Icon(Icons.arrow_back, color: ColorConstants.white),
        onPressed: () => Get.back(),
      ),
      actions: [
        if (!_isLoading && _parent != null)
          IconButton(
            icon: const Icon(Icons.refresh, color: ColorConstants.white),
            onPressed: _loadParent,
            tooltip: 'تحديث البيانات',
          ),
      ],
    );
  }

  Widget _buildBody(BuildContext context) {
    if (_isLoading) {
      return const Center(
        child: LoadingWidget(message: 'جاري تحميل بيانات ولي الأمر...'),
      );
    }

    if (_parent == null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.grey.shade400,
            ),
            const SizedBox(height: 16),
            Text(
              'لم يتم العثور على ولي الأمر',
              style: TextStyle(
                fontSize: 18,
                color: Colors.grey.shade600,
              ),
            ),
            const SizedBox(height: 24),
            ElevatedButton(
              onPressed: () => Get.back(),
              style: ElevatedButton.styleFrom(
                backgroundColor: ColorConstants.primary,
                foregroundColor: ColorConstants.white,
              ),
              child: const Text('العودة'),
            ),
          ],
        ),
      );
    }

    return Obx(() => ParentForm(
      parent: _parent,
      onSubmit: _handleSubmit,
      isLoading: _controller.isLoading,
    ));
  }

  Future<void> _handleSubmit(Parent parent) async {
    final success = await _controller.updateParent(parent);
    if (success) {
      Get.back();
      Get.snackbar(
        'نجح',
        'تم تحديث ولي الأمر بنجاح',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: ColorConstants.success.withValues(alpha: 0.1),
        colorText: ColorConstants.success,
      );
    }
  }
}
