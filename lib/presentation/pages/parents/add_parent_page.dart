import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../controllers/parents_controller.dart';
import '../../widgets/layout/responsive_sidebar.dart';
import '../../widgets/parents/parent_form.dart';
import '../../../core/constants/color_constants.dart';
import '../../../domain/entities/parent.dart';

/// Add parent page with responsive design
/// Following Single Responsibility Principle by focusing only on parent creation
class AddParentPage extends StatelessWidget {
  const AddParentPage({super.key});

  @override
  Widget build(BuildContext context) {
    final ParentsController controller = Get.find<ParentsController>();

    return ResponsiveSidebar(
      child: Scaffold(
        backgroundColor: Theme.of(context).brightness == Brightness.dark 
            ? ColorConstants.backgroundDark 
            : ColorConstants.background,
        appBar: _buildAppBar(context),
        body: Obx(() => ParentForm(
          onSubmit: _handleSubmit,
          isLoading: controller.isLoading,
        )),
      ),
    );
  }

  PreferredSizeWidget _buildAppBar(BuildContext context) {
    return AppBar(
      title: const Text(
        'إضافة ولي أمر جديد',
        style: TextStyle(
          fontWeight: FontWeight.w600,
          color: ColorConstants.white,
        ),
      ),
      backgroundColor: ColorConstants.primary,
      elevation: 0,
      leading: IconButton(
        icon: const Icon(Icons.arrow_back, color: ColorConstants.white),
        onPressed: () => Get.back(),
      ),
    );
  }

  Future<void> _handleSubmit(Parent parent) async {
    final ParentsController controller = Get.find<ParentsController>();
    final success = await controller.createParent(parent);
    if (success) {
      Get.back();
      Get.snackbar(
        'نجح',
        'تم إنشاء ولي الأمر بنجاح',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: ColorConstants.success.withValues(alpha: 0.1),
        colorText: ColorConstants.success,
      );
    }
  }
}
